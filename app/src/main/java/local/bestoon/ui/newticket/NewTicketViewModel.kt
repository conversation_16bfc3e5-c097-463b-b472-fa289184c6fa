package local.bestoon.ui.newticket

import android.content.Context
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import local.bestoon.data.model.CategoryResponse
import local.bestoon.data.model.Priority
import local.bestoon.data.model.Ticket
import local.bestoon.data.repository.NewTicketRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the new ticket screen.
 */
class NewTicketViewModel : ViewModel() {

    private val newTicketRepository = NewTicketRepository()

    // Categories
    private val _categories = MutableLiveData<List<CategoryResponse>>()
    val categories: LiveData<List<CategoryResponse>> = _categories

    // Priorities
    private val _priorities = MutableLiveData<List<Priority>>()
    val priorities: LiveData<List<Priority>> = _priorities

    // Loading state
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // Error state
    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    // Ticket creation result
    private val _ticketCreationResult = MutableLiveData<TicketCreationResult>()
    val ticketCreationResult: LiveData<TicketCreationResult> = _ticketCreationResult

    // Selected file
    private val _selectedFile = MutableLiveData<Uri?>()
    val selectedFile: LiveData<Uri?> = _selectedFile

    /**
     * Fetches the list of available categories.
     *
     * @param token The authentication token.
     */
    fun fetchCategories(token: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = newTicketRepository.getCategories(token)

                result.fold(
                    onSuccess = { categoriesList ->
                        _categories.value = categoriesList
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to fetch categories"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "An unexpected error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Fetches the list of available priorities.
     *
     * @param token The authentication token.
     */
    fun fetchPriorities(token: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = newTicketRepository.getPriorities(token)

                result.fold(
                    onSuccess = { prioritiesList ->
                        _priorities.value = prioritiesList
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Failed to fetch priorities"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "An unexpected error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Creates a new ticket.
     *
     * @param token The authentication token.
     * @param title The title of the ticket.
     * @param message The message content of the ticket.
     * @param category The category of the ticket (persian_name).
     * @param priority The priority of the ticket (value).
     * @param context The context to use for file operations (optional).
     */
    fun createTicket(
        token: String,
        title: String,
        message: String,
        category: String,
        priority: String,
        context: Context? = null
    ) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val fileUri = _selectedFile.value

                val result = if (fileUri != null && context != null) {
                    // Create ticket with file
                    newTicketRepository.createTicketWithFile(
                        token, title, message, category, priority, fileUri, context
                    )
                } else {
                    // Create ticket without file
                    newTicketRepository.createTicket(token, title, message, category, priority)
                }

                result.fold(
                    onSuccess = { ticket ->
                        _ticketCreationResult.value = TicketCreationResult.Success(ticket)
                        // Clear selected file after successful creation
                        _selectedFile.value = null
                    },
                    onFailure = { exception ->
                        _ticketCreationResult.value = TicketCreationResult.Error(
                            exception.message ?: "Failed to create ticket"
                        )
                    }
                )
            } catch (e: Exception) {
                _ticketCreationResult.value = TicketCreationResult.Error(
                    e.message ?: "An unexpected error occurred"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Sets the selected file.
     *
     * @param uri The URI of the selected file.
     */
    fun setSelectedFile(uri: Uri?) {
        _selectedFile.value = uri
    }

    /**
     * Validates the input fields for creating a new ticket.
     *
     * @param title The title of the ticket.
     * @param message The message content of the ticket.
     * @param category The selected category.
     * @param priority The selected priority.
     * @return A pair of booleans indicating whether the title and message are valid.
     */
    fun validateInputs(
        title: String,
        message: String,
        category: String?,
        priority: String?
    ): NewTicketValidationResult {
        val isTitleValid = title.isNotBlank()
        val isMessageValid = message.isNotBlank()
        val isCategoryValid = !category.isNullOrBlank()
        val isPriorityValid = !priority.isNullOrBlank()

        return NewTicketValidationResult(
            isTitleValid,
            isMessageValid,
            isCategoryValid,
            isPriorityValid
        )
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Data class representing the validation result for a new ticket.
     */
    data class NewTicketValidationResult(
        val isTitleValid: Boolean,
        val isMessageValid: Boolean,
        val isCategoryValid: Boolean,
        val isPriorityValid: Boolean
    )

    /**
     * Sealed class representing the result of a ticket creation attempt.
     */
    sealed class TicketCreationResult {
        /**
         * Represents a successful ticket creation.
         *
         * @property ticket The created ticket.
         */
        data class Success(val ticket: Ticket) : TicketCreationResult()

        /**
         * Represents a failed ticket creation.
         *
         * @property errorMessage The error message describing why the creation failed.
         */
        data class Error(val errorMessage: String) : TicketCreationResult()
    }
}
