package local.bestoon.ui.reports

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import local.bestoon.data.SessionManager
import local.bestoon.data.api.RetrofitClient
import local.bestoon.data.model.ReportsResponse
import local.bestoon.databinding.FragmentReportsBinding
import local.bestoon.utils.PersianUtils
import java.text.NumberFormat
import java.util.Locale

/**
 * Fragment for displaying reports data.
 */
class ReportsFragment : Fragment() {

    private var _binding: FragmentReportsBinding? = null
    private val binding get() = _binding!!

    private lateinit var reportsViewModel: ReportsViewModel
    private lateinit var sessionManager: SessionManager

    // Current selected values for comparison
    private var currentSelectedYear: Int = 0
    private var currentSelectedMonthStart: Int = 0
    private var currentSelectedMonthEnd: Int? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentReportsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Initialize SessionManager
        sessionManager = SessionManager(requireContext())

        // Initialize ViewModel
        val apiService = RetrofitClient.getApiService()
        val factory = ReportsViewModelFactory(apiService, sessionManager)
        reportsViewModel = ViewModelProvider(this, factory)[ReportsViewModel::class.java]

        // Setup observers
        setupObservers()

        // Load initial data
        reportsViewModel.loadReportsData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun setupObservers() {
        reportsViewModel.reportsData.observe(viewLifecycleOwner) { data ->
            updateUI(data)
        }

        reportsViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        }

        reportsViewModel.errorMessage.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage.isNotEmpty()) {
                binding.errorMessage.text = errorMessage
                binding.errorMessage.visibility = View.VISIBLE
                Toast.makeText(requireContext(), errorMessage, Toast.LENGTH_SHORT).show()
            } else {
                binding.errorMessage.visibility = View.GONE
            }
        }
    }

    private fun updateUI(data: ReportsResponse) {
        // Update current selected values
        currentSelectedYear = data.chosenyear
        currentSelectedMonthStart = data.chosenmonthstart
        currentSelectedMonthEnd = data.chosenmonthend

        // Setup year spinner
        setupYearSpinner(data.years, data.chosenyear)

        // Setup month spinners
        setupMonthSpinners(data.namesOfMonthsPersian, data.chosenmonthstart, data.chosenmonthend)

        // Update cards data
        updateCardsData(data)
    }

    private fun setupYearSpinner(years: List<Int>, selectedYear: Int) {
        // Convert years to Persian numerals
        val persianYears = years.map { PersianUtils.convertToPersianNumerals(it.toString()) }

        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, persianYears)
        binding.yearSpinner.setAdapter(adapter)

        // Set selected year
        val selectedIndex = years.indexOf(selectedYear)
        if (selectedIndex >= 0) {
            binding.yearSpinner.setText(persianYears[selectedIndex], false)
        }

        // Set listener for year changes
        binding.yearSpinner.setOnItemClickListener { _, _, position, _ ->
            val selectedYearValue = years[position]
            // Only update if the selected year is different from the current one
            if (currentSelectedYear != selectedYearValue) {
                reportsViewModel.updateFilters(year = selectedYearValue, monthStart = null, monthEnd = null)
            }
        }
    }

    private fun setupMonthSpinners(monthNames: List<String>, selectedMonthStart: Int, selectedMonthEnd: Int) {
        // Setup month start spinner
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, monthNames)
        binding.monthStartSpinner.setAdapter(adapter)

        // Set selected month start
        val selectedStartIndex = selectedMonthStart - 1 // Convert to 0-based
        if (selectedStartIndex >= 0 && selectedStartIndex < monthNames.size) {
            binding.monthStartSpinner.setText(monthNames[selectedStartIndex], false)
        }

        // Setup month end spinner with empty option
        val monthNamesWithEmpty = listOf("") + monthNames
        val endAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_dropdown_item_1line, monthNamesWithEmpty)
        binding.monthEndSpinner.setAdapter(endAdapter)

        // Set selected month end
        if (selectedMonthEnd > 0) {
            val selectedEndIndex = selectedMonthEnd // selectedMonthEnd is already 1-based, but we added empty option at index 0
            if (selectedEndIndex < monthNamesWithEmpty.size) {
                binding.monthEndSpinner.setText(monthNamesWithEmpty[selectedEndIndex], false)
            }
        } else {
            binding.monthEndSpinner.setText("", false)
        }

        // Set listeners for month changes
        binding.monthStartSpinner.setOnItemClickListener { _, _, position, _ ->
            val selectedMonthValue = position + 1 // Convert to 1-based
            // Only update if the selected month start is different from the current one
            if (currentSelectedMonthStart != selectedMonthValue) {
                reportsViewModel.updateFilters(year = null, monthStart = selectedMonthValue, monthEnd = null)
            }
        }

        binding.monthEndSpinner.setOnItemClickListener { _, _, position, _ ->
            if (position == 0) {
                // Empty option selected - clear month end filter
                if (currentSelectedMonthEnd != null) {
                    reportsViewModel.clearMonthEndFilter()
                }
            } else {
                val selectedMonthValue = position // position is already correct since we added empty option at index 0
                // Only update if the selected month end is different from the current one
                if (currentSelectedMonthEnd != selectedMonthValue) {
                    reportsViewModel.updateFilters(year = null, monthStart = null, monthEnd = selectedMonthValue)
                }
            }
        }
    }

    private fun updateCardsData(data: ReportsResponse) {
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))

        // Card 1: Income Year
        binding.textIncomeYearDate.text = data.chosenyearPersian
        binding.textIncomeYearMax.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.incomeReportYear.maximum))
        binding.textIncomeYearMin.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.incomeReportYear.minimum))
        binding.textIncomeYearAvg.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.incomeReportYear.average))
        binding.textIncomeYearTotal.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.incomeReportYear.total))

        // Card 2: Income Month
        val monthDateFormat = if (data.chosenmonthend == 0) {
            "${data.chosenyearPersian}/${data.chosenmonthstartZeroedPersian}"
        } else {
            // using the arrow (->) in the string caused issues with RTL
            // i.e. intead of this:
            // <date1> -> <date2>
            // we ended up with this:
            // <date2> <- <date1>
            // so we had to force LTR direction using \u202D and \u202C
            "\u202D${data.chosenyearPersian}/${data.chosenmonthstartZeroedPersian} -> ${data.chosenyearPersian}/${data.chosenmonthendZeroedPersian}\u202C"
        }
        binding.textIncomeMonthDate.text = monthDateFormat
        binding.textIncomeMonthMax.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.incomeReportMonth.maximum))
        binding.textIncomeMonthMin.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.incomeReportMonth.minimum))
        binding.textIncomeMonthAvg.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.incomeReportMonth.average))
        binding.textIncomeMonthTotal.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.incomeReportMonth.total))

        // Card 3: Expenditure Year
        binding.textExpenditureYearDate.text = data.chosenyearPersian
        binding.textExpenditureYearMax.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.expenditureReportYear.maximum))
        binding.textExpenditureYearMin.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.expenditureReportYear.minimum))
        binding.textExpenditureYearAvg.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.expenditureReportYear.average))
        binding.textExpenditureYearTotal.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.expenditureReportYear.total))

        // Card 4: Expenditure Month
        binding.textExpenditureMonthDate.text = monthDateFormat
        binding.textExpenditureMonthMax.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.expenditureReportMonth.maximum))
        binding.textExpenditureMonthMin.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.expenditureReportMonth.minimum))
        binding.textExpenditureMonthAvg.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.expenditureReportMonth.average))
        binding.textExpenditureMonthTotal.text = PersianUtils.convertToPersianNumerals(numberFormat.format(data.expenditureReportMonth.total))

        // Update top boxes
        updateTopBoxes(data, numberFormat)
    }

    private fun updateTopBoxes(data: ReportsResponse, numberFormat: NumberFormat) {
        // Top Income Year
        binding.textTopIncomeYearDate.text = data.chosenyearPersian
        populateTopBox(binding.layoutTopIncomeYearContent, data.topIncomesOfYear, numberFormat)

        // Top Income Month
        val monthDateFormat = if (data.chosenmonthend == 0) {
            "${data.chosenyearPersian}/${data.chosenmonthstartZeroedPersian}"
        } else {
            "\u202D${data.chosenyearPersian}/${data.chosenmonthstartZeroedPersian} -> ${data.chosenyearPersian}/${data.chosenmonthendZeroedPersian}\u202C"
        }
        binding.textTopIncomeMonthDate.text = monthDateFormat
        populateTopBox(binding.layoutTopIncomeMonthContent, data.topIncomesOfMonth, numberFormat)

        // Top Expenditure Year
        binding.textTopExpenditureYearDate.text = data.chosenyearPersian
        populateTopBox(binding.layoutTopExpenditureYearContent, data.topExpendituresOfYear, numberFormat)

        // Top Expenditure Month
        binding.textTopExpenditureMonthDate.text = monthDateFormat
        populateTopBox(binding.layoutTopExpenditureMonthContent, data.topExpendituresOfMonth, numberFormat)
    }

    private fun populateTopBox(container: LinearLayout, topData: Map<String, Int>, numberFormat: NumberFormat) {
        container.removeAllViews()

        topData.forEach { (amount, count) ->
            val rowLayout = LinearLayout(requireContext()).apply {
                orientation = LinearLayout.HORIZONTAL
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                ).apply {
                    bottomMargin = 8
                }
            }

            val amountText = TextView(requireContext()).apply {
                layoutParams = LinearLayout.LayoutParams(
                    0,
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    1f
                )
                text = PersianUtils.convertToPersianNumerals(numberFormat.format(amount.toInt()))
                textSize = 12f
            }

            val countText = TextView(requireContext()).apply {
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT
                )
                text = PersianUtils.convertToPersianNumerals(count.toString())
                textSize = 12f
            }

            rowLayout.addView(amountText)
            rowLayout.addView(countText)
            container.addView(rowLayout)
        }
    }
}
