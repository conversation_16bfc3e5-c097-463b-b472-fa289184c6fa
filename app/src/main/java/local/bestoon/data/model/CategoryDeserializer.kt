package local.bestoon.data.model

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import java.lang.reflect.Type

/**
 * Custom deserializer for handling category field in tickets.
 * This deserializer can handle both string values and Category objects.
 */
class CategoryDeserializer : JsonDeserializer<Category?> {
    override fun deserialize(
        json: JsonElement?,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Category? {
        // If the element is null, return null
        if (json == null || json.isJsonNull) {
            return null
        }

        // If the element is a string, create a Category with the string as the name
        if (json.isJsonPrimitive && json.asJsonPrimitive.isString) {
            val categoryName = json.asString
            return Category(
                id = 0, // Default ID since we don't have one
                name = categoryName,
                persianName = categoryName // Use the same value for persianName
            )
        }

        // If the element is an object, deserialize it as a Category
        return if (json.isJsonObject) {
            context?.deserialize(json, Category::class.java)
        } else {
            null
        }
    }
}
