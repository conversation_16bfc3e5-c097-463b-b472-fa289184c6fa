<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <!-- Icon -->
    <ImageView
        android:id="@+id/iv_calendar_icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="16dp"
        android:src="@drawable/ic_calendar"
        android:tint="?attr/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center"
        android:text="انتخاب تاریخ"
        android:textAppearance="?attr/textAppearanceHeadline6"
        android:textColor="?attr/colorOnSurface"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_calendar_icon" />

    <!-- Date Pickers Container -->
    <LinearLayout
        android:id="@+id/date_pickers_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Year Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="سال"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/year_picker"
                android:layout_width="wrap_content"
                android:layout_height="120dp" />

        </LinearLayout>

        <!-- Month Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ماه"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/month_picker"
                android:layout_width="wrap_content"
                android:layout_height="120dp" />

        </LinearLayout>

        <!-- Day Picker -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="روز"
                android:textSize="14sp"
                android:textColor="?attr/colorOnSurface"
                android:layout_marginBottom="8dp" />

            <NumberPicker
                android:id="@+id/day_picker"
                android:layout_width="wrap_content"
                android:layout_height="120dp" />

        </LinearLayout>

    </LinearLayout>

    <!-- Divider -->
    <View
        android:id="@+id/divider"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="20dp"
        android:alpha="0.12"
        android:background="?attr/colorOnSurface"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/date_pickers_container" />

    <!-- Button Container -->
    <LinearLayout
        android:id="@+id/buttons_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="end"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider">

        <Button
            android:id="@+id/btn_cancel"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="لغو"
            android:textColor="?attr/colorOnSurface" />

        <Button
            android:id="@+id/btn_ok"
            style="@style/Widget.MaterialComponents.Button"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:backgroundTint="?attr/colorPrimary"
            android:text="تأیید"
            android:textColor="@android:color/white" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
