package local.bestoon.ui.profile

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import local.bestoon.R
import local.bestoon.data.SessionManager
import local.bestoon.databinding.DialogChangePasswordBinding
import local.bestoon.databinding.FragmentProfileBinding

/**
 * Fragment for displaying the user's profile information.
 */
class ProfileFragment : Fragment() {

    private var _binding: FragmentProfileBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: ProfileViewModel
    private lateinit var sessionManager: SessionManager
    private val TAG = "ProfileFragment"

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this)[ProfileViewModel::class.java]
        sessionManager = SessionManager(requireContext())

        _binding = FragmentProfileBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupObservers()
        setupChangePasswordButton()
        loadProfile()
    }

    /**
     * Sets up the change password button.
     */
    private fun setupChangePasswordButton() {
        binding.btnChangePassword.setOnClickListener {
            showChangePasswordDialog()
        }
    }

    /**
     * Sets up the observers for the ViewModel's LiveData.
     */
    private fun setupObservers() {
        viewModel.profileState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is ProfileViewModel.ProfileState.Loading -> {
                    showLoading()
                }
                is ProfileViewModel.ProfileState.Success -> {
                    hideLoading()
                    displayProfile(state)
                }
                is ProfileViewModel.ProfileState.Error -> {
                    hideLoading()
                    showError(state.message)
                }
            }
        }

        viewModel.passwordChangeState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is ProfileViewModel.PasswordChangeState.Loading -> {
                    // Loading state is handled in the dialog
                }
                is ProfileViewModel.PasswordChangeState.Success -> {
                    // Success state is handled in the dialog
                }
                is ProfileViewModel.PasswordChangeState.Error -> {
                    // Error state is handled in the dialog
                }
                null -> {
                    // Initial state, do nothing
                }
            }
        }
    }

    /**
     * Loads the profile data from the API.
     */
    private fun loadProfile() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchProfile(token)
        } else {
            showError("Authentication token not found")
        }
    }

    /**
     * Displays the profile data in the UI.
     *
     * @param state The success state containing the profile data.
     */
    private fun displayProfile(state: ProfileViewModel.ProfileState.Success) {
        val profile = state.profile

        // Set header information
        binding.profileName.text = "${profile.firstName} ${profile.lastName}"
        binding.profilePosition.text = if (profile.position != null && profile.company != null) {
            "${profile.position} at ${profile.company}"
        } else if (profile.position != null) {
            profile.position
        } else if (profile.company != null) {
            "at ${profile.company}"
        } else {
            ""
        }

        // Set card information
        binding.profileUsername.text = profile.username
        binding.profileEmail.text = profile.email
        binding.profileCompany.text = profile.company ?: "نامعلوم"
        binding.profilePositionText.text = profile.position ?: "نامعلوم"
        binding.profileFirstName.text = profile.firstName
        binding.profileLastName.text = profile.lastName
        binding.profileDescription.text = profile.description ?: "نامعلوم"
        binding.profileGender.text = when (profile.gender) {
            "M" -> "مذکر"
            "F" -> "مونث"
            else -> profile.gender
        }
        binding.profileIsSuperuser.text = if (profile.isSuperuser) "بله" else "خیر"
        binding.profileIsLimitedAdmin.text = if (profile.isLimitedAdmin) "بله" else "خیر"
        // binding.profileUserId.text = profile.id.toString()
        binding.profileShortUuid.text = profile.shortUuid

        // Show all cards
        binding.cardPersonalInfo.visibility = View.VISIBLE
        binding.cardContactInfo.visibility = View.VISIBLE
        binding.cardCompanyInfo.visibility = View.VISIBLE
        binding.cardInfo.visibility = View.VISIBLE
    }

    /**
     * Shows the loading indicator and hides other views.
     */
    private fun showLoading() {
        binding.progressBar.visibility = View.VISIBLE
        binding.cardPersonalInfo.visibility = View.GONE
        binding.cardContactInfo.visibility = View.GONE
        binding.cardCompanyInfo.visibility = View.GONE
        binding.cardInfo.visibility = View.GONE
        binding.errorText.visibility = View.GONE
    }

    /**
     * Hides the loading indicator.
     */
    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to display.
     */
    private fun showError(message: String) {
        binding.errorText.text = message
        binding.errorText.visibility = View.VISIBLE
        binding.cardPersonalInfo.visibility = View.GONE
        binding.cardContactInfo.visibility = View.GONE
        binding.cardCompanyInfo.visibility = View.GONE
        binding.cardInfo.visibility = View.GONE
    }

    /**
     * Shows the change password dialog.
     */
    private fun showChangePasswordDialog() {
        val dialogBinding = DialogChangePasswordBinding.inflate(layoutInflater)
        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogBinding.root)
            .setCancelable(true)
            .create()

        // Set up the dialog
        dialogBinding.apply {
            // Set up the cancel button
            btnCancel.setOnClickListener {
                dialog.dismiss()
            }

            // Set up the change password button
            btnChangePassword.setOnClickListener {
                val oldPassword = etOldPassword.text.toString()
                val newPassword = etNewPassword.text.toString()
                val confirmPassword = etConfirmPassword.text.toString()

                // Reset error message
                tvErrorMessage.visibility = View.GONE

                // Validate inputs
                if (oldPassword.isBlank() || newPassword.isBlank() || confirmPassword.isBlank()) {
                    tvErrorMessage.text = getString(R.string.profile__error_changing_password) + ": All fields are required"
                    tvErrorMessage.visibility = View.VISIBLE
                    return@setOnClickListener
                }

                if (newPassword != confirmPassword) {
                    tvErrorMessage.text = getString(R.string.profile__error_changing_password) + ": New passwords do not match"
                    tvErrorMessage.visibility = View.VISIBLE
                    return@setOnClickListener
                }

                if (newPassword.length < 8) {
                    tvErrorMessage.text = getString(R.string.profile__error_changing_password) + ": Password must be at least 8 characters long"
                    tvErrorMessage.visibility = View.VISIBLE
                    return@setOnClickListener
                }

                // Get the token
                val token = sessionManager.getAuthToken()
                if (token == null) {
                    tvErrorMessage.text = getString(R.string.profile__error_changing_password) + ": Authentication token not found"
                    tvErrorMessage.visibility = View.VISIBLE
                    return@setOnClickListener
                }

                // Change the password
                viewModel.changePassword(token, oldPassword, newPassword, confirmPassword)

                // Observe the password change state
                viewModel.passwordChangeState.observe(viewLifecycleOwner) { state ->
                    when (state) {
                        is ProfileViewModel.PasswordChangeState.Loading -> {
                            progressBar.visibility = View.VISIBLE
                            tvErrorMessage.visibility = View.GONE
                            btnChangePassword.isEnabled = false
                            btnCancel.isEnabled = false
                        }
                        is ProfileViewModel.PasswordChangeState.Success -> {
                            progressBar.visibility = View.GONE
                            dialog.dismiss()
                            Toast.makeText(
                                requireContext(),
                                getString(R.string.profile__password_changed_successfully),
                                Toast.LENGTH_SHORT
                            ).show()
                            viewModel.resetPasswordChangeState()
                        }
                        is ProfileViewModel.PasswordChangeState.Error -> {
                            progressBar.visibility = View.GONE
                            tvErrorMessage.text = getString(R.string.profile__error_changing_password) + ": ${state.message}"
                            tvErrorMessage.visibility = View.VISIBLE
                            btnChangePassword.isEnabled = true
                            btnCancel.isEnabled = true
                        }
                        null -> {
                            // Initial state, do nothing
                        }
                    }
                }
            }
        }

        dialog.show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
