package local.bestoon.data.repository

import local.bestoon.data.api.RetrofitClient
import local.bestoon.data.model.NewEventRequest
import local.bestoon.utils.TokenUtils

/**
 * Repository class that handles new event-related operations.
 */
class NewEventRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Creates a new event.
     *
     * @param token The authentication token.
     * @param request The new event request data.
     * @return A Result containing either success or an Exception.
     */
    suspend fun createEvent(token: String, request: NewEventRequest): Result<Unit> {
        return safeApiCallUnit {
            apiService.createEvent(TokenUtils.formatToken(token), request)
        }
    }
}
