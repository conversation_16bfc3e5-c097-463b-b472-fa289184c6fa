package local.bestoon.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing a password change request.
 *
 * @property oldPassword The current password of the user.
 * @property newPassword1 The new password.
 * @property newPassword2 The confirmation of the new password.
 */
data class PasswordChangeRequest(
    @SerializedName("old_password") val oldPassword: String,
    @SerializedName("new_password1") val newPassword1: String,
    @SerializedName("new_password2") val newPassword2: String
)
