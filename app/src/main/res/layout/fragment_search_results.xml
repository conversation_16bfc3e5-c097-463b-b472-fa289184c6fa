<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="6dp"
    android:background="?attr/colorSurface"
    tools:context=".ui.search.SearchResultsFragment">

    <!-- Search Query -->
    <TextView
        android:id="@+id/tv_search_query"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_margin="4dp"
        android:textAppearance="?attr/textAppearanceBody1"
        tools:text="Search query: hello" />

    <!-- Tabs Section -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        app:tabMode="fixed"
        app:tabGravity="fill" />

    <!-- ViewPager for Tab Content -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:minHeight="400dp" />

    <!-- Error View -->
    <TextView
        android:id="@+id/tv_error"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_margin="4dp"
        android:textAlignment="center"
        android:textColor="@android:color/holo_red_dark"
        android:visibility="gone"
        tools:text="Error message"
        tools:visibility="visible" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="16dp"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>
