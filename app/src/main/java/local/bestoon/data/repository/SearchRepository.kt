package local.bestoon.data.repository

import local.bestoon.data.api.RetrofitClient
import local.bestoon.data.model.SearchResult
import local.bestoon.utils.TokenUtils

/**
 * Repository class that handles search-related operations.
 */
class SearchRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    suspend fun search(token: String, query: String): Result<SearchResult> {
        return safeApiCall {
            apiService.search(TokenUtils.formatToken(token), query)
        }
    }
}
