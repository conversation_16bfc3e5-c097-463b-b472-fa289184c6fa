package local.bestoon.ui.search

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.PopupMenu
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.RecyclerView
import local.bestoon.R
import local.bestoon.data.model.SearchTransaction
import local.bestoon.data.model.IncomeObject
import local.bestoon.data.model.ExpenditureObject
import java.text.NumberFormat
import java.util.Locale

/**
 * Adapter for displaying search transaction results using the existing income/expenditure layout.
 */
class SearchTransactionAdapter(
    private val onDeleteClick: (String) -> Unit,
    private val onEditClick: (String, SearchTransaction) -> Unit
) : RecyclerView.Adapter<SearchTransactionAdapter.SearchTransactionViewHolder>() {

    private var transactionList: List<SearchTransaction> = emptyList()

    class SearchTransactionViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val textShortUuid: TextView = itemView.findViewById(R.id.text_short_uuid)
        val textTitle: TextView = itemView.findViewById(R.id.text_title)
        val textAmount: TextView = itemView.findViewById(R.id.text_amount)
        val textBank: TextView = itemView.findViewById(R.id.text_bank)
        val textCategory: TextView = itemView.findViewById(R.id.text_category)
        val textTags: TextView = itemView.findViewById(R.id.text_tags)
        val textCreated: TextView = itemView.findViewById(R.id.text_created)
        val btnMenu: ImageButton = itemView.findViewById(R.id.btn_menu)
    }

    override fun getItemViewType(position: Int): Int {
        // Return different view types for income (I) and expenditure (E)
        return if (transactionList[position].mode == "I") 0 else 1
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchTransactionViewHolder {
        // Use income layout for income transactions, expenditure layout for expenditure transactions
        val layoutId = if (viewType == 0) R.layout.item_income else R.layout.item_expenditure
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return SearchTransactionViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchTransactionViewHolder, position: Int) {
        val transaction = transactionList[position]
        
        holder.textShortUuid.text = transaction.shortUuid
        holder.textBank.text = transaction.bankInfo?.title ?: "نامشخص"
        holder.textCreated.text = transaction.slashedDatePersian

        // Handle title - hide if empty or null
        if (!transaction.title.isNullOrEmpty()) {
            holder.textTitle.text = transaction.title
            holder.textTitle.visibility = View.VISIBLE
        } else {
            holder.textTitle.visibility = View.GONE
        }

        holder.textCategory.text = "دسته‌بندی: ${transaction.categoryInfo?.title ?: "نامشخص"}"

        // Handle tags - hide if empty
        if (transaction.tagsNames.isNotEmpty()) {
            holder.textTags.text = "برچسب‌ها: ${transaction.tagsNames.joinToString(", ")}"
            holder.textTags.visibility = View.VISIBLE
        } else {
            holder.textTags.visibility = View.GONE
        }

        // Format amount with Persian number formatting
        val numberFormat = NumberFormat.getNumberInstance(Locale("fa", "IR"))
        holder.textAmount.text = "${numberFormat.format(transaction.amount)} تومان"

        // Setup menu button click listener
        holder.btnMenu.setOnClickListener { view ->
            val popup = PopupMenu(view.context, view)
            popup.menuInflater.inflate(R.menu.item_menu, popup.menu)
            popup.setOnMenuItemClickListener { menuItem ->
                when (menuItem.itemId) {
                    R.id.action_edit -> {
                        onEditClick(transaction.shortUuid, transaction)
                        true
                    }
                    R.id.action_delete -> {
                        showDeleteConfirmationDialog(view, transaction.shortUuid)
                        true
                    }
                    else -> false
                }
            }
            popup.show()
        }
    }

    override fun getItemCount(): Int = transactionList.size

    fun updateData(newTransactionList: List<SearchTransaction>) {
        transactionList = newTransactionList
        notifyDataSetChanged()
    }

    /**
     * Shows a confirmation dialog before deleting the transaction item.
     */
    private fun showDeleteConfirmationDialog(view: View, shortUuid: String) {
        val dialogView = LayoutInflater.from(view.context).inflate(R.layout.dialog_delete_confirm, null)
        val titleTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_title)
        val messageTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_message)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        // Set the title and message
        titleTextView.text = view.context.getString(R.string.delete_transaction_confirmation_title)
        messageTextView.text = view.context.getString(R.string.delete_transaction_confirmation_message)

        val dialog = AlertDialog.Builder(view.context)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            onDeleteClick(shortUuid)
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }
}
