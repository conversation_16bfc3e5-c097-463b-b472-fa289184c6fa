package local.bestoon.ui.home

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import local.bestoon.R
import local.bestoon.data.model.ExpenditureObject
import local.bestoon.data.model.EventObject
import local.bestoon.data.model.IncomeObject

class HomePagerAdapter(
    private val onDeleteTransaction: (String) -> Unit,
    private val onEditTransaction: (String, IncomeObject?, ExpenditureObject?) -> Unit,
    private val onDeleteEvent: (String) -> Unit,
    private val onEditEvent: (String, EventObject) -> Unit
) : RecyclerView.Adapter<HomePagerAdapter.TabViewHolder>() {

    private var incomeList: List<IncomeObject> = emptyList()
    private var expenditureList: List<ExpenditureObject> = emptyList()
    private var eventList: List<EventObject> = emptyList()

    // Keep references to adapters to avoid recreating them
    private var incomeAdapter: IncomeAdapter? = null
    private var expenditureAdapter: ExpenditureAdapter? = null
    private var eventAdapter: EventAdapter? = null

    sealed class TabViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        class IncomeViewHolder(itemView: View) : TabViewHolder(itemView) {
            val recyclerView: RecyclerView = itemView.findViewById(R.id.recycler_income)
        }

        class ExpenditureViewHolder(itemView: View) : TabViewHolder(itemView) {
            val recyclerView: RecyclerView = itemView.findViewById(R.id.recycler_expenditure)
        }

        class EventViewHolder(itemView: View) : TabViewHolder(itemView) {
            val recyclerView: RecyclerView = itemView.findViewById(R.id.recycler_events)
        }
    }

    override fun getItemViewType(position: Int): Int {
        return position // 0 = Income, 1 = Expenditure, 2 = Events
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TabViewHolder {
        return when (viewType) {
            0 -> {
                val view = LayoutInflater.from(parent.context).inflate(R.layout.tab_income, parent, false)
                TabViewHolder.IncomeViewHolder(view)
            }
            1 -> {
                val view = LayoutInflater.from(parent.context).inflate(R.layout.tab_expenditure, parent, false)
                TabViewHolder.ExpenditureViewHolder(view)
            }
            2 -> {
                val view = LayoutInflater.from(parent.context).inflate(R.layout.tab_events, parent, false)
                TabViewHolder.EventViewHolder(view)
            }
            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun onBindViewHolder(holder: TabViewHolder, position: Int) {
        when (holder) {
            is TabViewHolder.IncomeViewHolder -> {
                holder.recyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
                if (incomeAdapter == null) {
                    incomeAdapter = IncomeAdapter(
                        incomeList,
                        onDeleteTransaction,
                        { shortUuid, income -> onEditTransaction(shortUuid, income, null) }
                    )
                    holder.recyclerView.adapter = incomeAdapter
                } else {
                    incomeAdapter?.updateData(incomeList)
                    if (holder.recyclerView.adapter == null) {
                        holder.recyclerView.adapter = incomeAdapter
                    }
                }
            }
            is TabViewHolder.ExpenditureViewHolder -> {
                holder.recyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
                if (expenditureAdapter == null) {
                    expenditureAdapter = ExpenditureAdapter(
                        expenditureList,
                        onDeleteTransaction,
                        { shortUuid, expenditure -> onEditTransaction(shortUuid, null, expenditure) }
                    )
                    holder.recyclerView.adapter = expenditureAdapter
                } else {
                    expenditureAdapter?.updateData(expenditureList)
                    if (holder.recyclerView.adapter == null) {
                        holder.recyclerView.adapter = expenditureAdapter
                    }
                }
            }
            is TabViewHolder.EventViewHolder -> {
                holder.recyclerView.layoutManager = LinearLayoutManager(holder.itemView.context)
                if (eventAdapter == null) {
                    eventAdapter = EventAdapter(eventList, onDeleteEvent, onEditEvent)
                    holder.recyclerView.adapter = eventAdapter
                } else {
                    eventAdapter?.updateData(eventList)
                    if (holder.recyclerView.adapter == null) {
                        holder.recyclerView.adapter = eventAdapter
                    }
                }
            }
        }
    }

    override fun getItemCount(): Int = 3 // Income, Expenditure, Events

    fun updateData(
        newIncomeList: List<IncomeObject>,
        newExpenditureList: List<ExpenditureObject>,
        newEventList: List<EventObject>
    ) {
        incomeList = newIncomeList
        expenditureList = newExpenditureList
        eventList = newEventList

        // Update existing adapters if they exist
        incomeAdapter?.updateData(newIncomeList)
        expenditureAdapter?.updateData(newExpenditureList)
        eventAdapter?.updateData(newEventList)

        notifyDataSetChanged()
    }
}
