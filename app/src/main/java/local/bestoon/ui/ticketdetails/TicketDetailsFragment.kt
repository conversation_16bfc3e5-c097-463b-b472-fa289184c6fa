package local.bestoon.ui.ticketdetails

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.util.Log
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import local.bestoon.R
import local.bestoon.data.SessionManager
import local.bestoon.data.model.CategoryResponse
import local.bestoon.data.model.Priority
import local.bestoon.data.model.Status
import local.bestoon.data.model.Ticket
import local.bestoon.data.model.TicketChange
import local.bestoon.data.model.TicketStatus
import local.bestoon.databinding.DialogRateTicketBinding
import local.bestoon.databinding.FragmentTicketDetailsBinding
import local.bestoon.utils.HtmlRenderer
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Fragment for displaying ticket details.
 */
class TicketDetailsFragment : Fragment() {

    private var _binding: FragmentTicketDetailsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: TicketDetailsViewModel
    private lateinit var chatResponseAdapter: ChatResponseAdapter
    private lateinit var changeAdapter: TicketChangeAdapter
    private lateinit var sessionManager: SessionManager

    private var shortUuid: String? = null
    private var selectedFileUri: Uri? = null
    private var ratingDialog: AlertDialog? = null
    private var isRatingClickListenerSetup = false
    private var currentPhotoUri: Uri? = null

    // File picker launcher
    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleSelectedFile(uri)
            }
        }
    }

    // Camera launcher
    private val cameraLauncher = registerForActivityResult(
        ActivityResultContracts.TakePicture()
    ) { success ->
        if (success && currentPhotoUri != null) {
            handleSelectedFile(currentPhotoUri!!)
        }
    }

    // Camera permission launcher
    private val cameraPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            openCamera()
        } else {
            Toast.makeText(
                requireContext(),
                getString(R.string.camera_permission_is_required),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(TicketDetailsViewModel::class.java)
        _binding = FragmentTicketDetailsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // Get the shortUuid from arguments
        shortUuid = arguments?.getString("shortUuid")

        sessionManager = SessionManager(requireContext())
        setupRecyclerView()
        setupChangesRecyclerView()
        setupReplyForm()
        observeViewModel()
        fetchTicketDetails()
    }

    /**
     * Sets up the RecyclerView for displaying ticket responses in chat style.
     */
    private fun setupRecyclerView() {
        // We'll initialize the adapter when we have the ticket data
        binding.recyclerResponses.apply {
            layoutManager = LinearLayoutManager(requireContext())
            isNestedScrollingEnabled = false
        }
    }

    /**
     * Sets up the RecyclerView for displaying ticket changes.
     */
    private fun setupChangesRecyclerView() {
        changeAdapter = TicketChangeAdapter()
        binding.recyclerChanges.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = changeAdapter
            isNestedScrollingEnabled = false
        }
    }

    /**
     * Sets up the reply form.
     */
    private fun setupReplyForm() {
        binding.btnSelectReplyFile.setOnClickListener {
            openFilePicker()
        }

        binding.btnTakeReplyPhoto.setOnClickListener {
            checkCameraPermissionAndTakePhoto()
        }

        binding.btnSubmitReply.setOnClickListener {
            submitReply()
        }
    }

    /**
     * Observes changes in the ViewModel.
     */
    private fun observeViewModel() {
        viewModel.state.observe(viewLifecycleOwner) { state ->
            when (state) {
                is TicketDetailsViewModel.TicketDetailsState.Loading -> {
                    showLoading()
                }
                is TicketDetailsViewModel.TicketDetailsState.Success -> {
                    hideLoading()
                    displayTicketDetails(state.ticket)
                    // Fetch categories, priorities, and statuses for the dropdowns
                    fetchCategories()
                    fetchPriorities()
                    fetchStatuses()

                    // Fetch author groups
                    fetchAuthorGroups(state.ticket.author.shortUuid)

                    // Fetch author full name
                    fetchAuthorFullName(state.ticket.author.shortUuid)

                    // Setup delete button visibility based on superuser status
                    setupDeleteButton()
                }
                is TicketDetailsViewModel.TicketDetailsState.Error -> {
                    hideLoading()
                    showError(state.message)
                }
            }
        }

        viewModel.deleteState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is TicketDetailsViewModel.DeleteState.Idle -> {
                    // Do nothing
                }
                is TicketDetailsViewModel.DeleteState.Loading -> {
                    // Show loading indicator if needed
                    showLoading()
                }
                is TicketDetailsViewModel.DeleteState.Success -> {
                    hideLoading()
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.ticket_deleted_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate to home after deletion
                    findNavController().navigate(R.id.nav_home)
                }
                is TicketDetailsViewModel.DeleteState.Error -> {
                    hideLoading()
                    Toast.makeText(
                        requireContext(),
                        state.message,
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.resetDeleteState()
                }
            }
        }

        viewModel.replyState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is TicketDetailsViewModel.ReplyState.Idle -> {
                    hideReplyLoading()
                    hideReplyError()
                }
                is TicketDetailsViewModel.ReplyState.Loading -> {
                    showReplyLoading()
                    hideReplyError()
                }
                is TicketDetailsViewModel.ReplyState.Success -> {
                    hideReplyLoading()
                    hideReplyError()
                    clearReplyForm()
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.reply_sent_successfully),
                        Toast.LENGTH_SHORT
                    ).show()
                }
                is TicketDetailsViewModel.ReplyState.Error -> {
                    hideReplyLoading()
                    showReplyError(state.message)
                }
            }
        }

        viewModel.categoryUpdateState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is TicketDetailsViewModel.CategoryUpdateState.Idle -> {
                    // Do nothing
                }
                is TicketDetailsViewModel.CategoryUpdateState.Loading -> {
                    // Show loading indicator if needed
                }
                is TicketDetailsViewModel.CategoryUpdateState.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.category_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.resetCategoryUpdateState()
                }
                is TicketDetailsViewModel.CategoryUpdateState.Error -> {
                    Toast.makeText(
                        requireContext(),
                        state.message,
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.resetCategoryUpdateState()
                }
            }
        }

        viewModel.categories.observe(viewLifecycleOwner) { categories ->
            if (categories.isNotEmpty()) {
                setupCategoryDropdown(categories)
            }
        }

        viewModel.priorityUpdateState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is TicketDetailsViewModel.PriorityUpdateState.Idle -> {
                    // Do nothing
                }
                is TicketDetailsViewModel.PriorityUpdateState.Loading -> {
                    // Show loading indicator if needed
                }
                is TicketDetailsViewModel.PriorityUpdateState.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.priority_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.resetPriorityUpdateState()
                }
                is TicketDetailsViewModel.PriorityUpdateState.Error -> {
                    Toast.makeText(
                        requireContext(),
                        state.message,
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.resetPriorityUpdateState()
                }
            }
        }

        viewModel.priorities.observe(viewLifecycleOwner) { priorities ->
            if (priorities.isNotEmpty()) {
                setupPriorityDropdown(priorities)
            }
        }

        viewModel.statusUpdateState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is TicketDetailsViewModel.StatusUpdateState.Idle -> {
                    // Do nothing
                }
                is TicketDetailsViewModel.StatusUpdateState.Loading -> {
                    // Show loading indicator if needed
                }
                is TicketDetailsViewModel.StatusUpdateState.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.status_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Check if we need to show the status alert message
                    val updatedTicket = state.ticket
                    when (updatedTicket.status) {
                        TicketStatus.CLOSED -> {
                            binding.cardStatusAlert.visibility = View.VISIBLE
                            binding.tvStatusAlertMessage.text = getString(R.string.ticket_closed_message)
                            binding.cardStatusAlert.setCardBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.holo_purple))

                            // Show reply form for closed tickets
                            binding.cardReplyForm.visibility = View.VISIBLE
                        }
                        TicketStatus.DISABLED -> {
                            binding.cardStatusAlert.visibility = View.VISIBLE
                            binding.tvStatusAlertMessage.text = getString(R.string.ticket_disabled_message)
                            binding.cardStatusAlert.setCardBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.darker_gray))

                            // Hide reply form for disabled tickets
                            binding.cardReplyForm.visibility = View.GONE
                        }
                        else -> {
                            binding.cardStatusAlert.visibility = View.GONE

                            // Show reply form for other statuses
                            binding.cardReplyForm.visibility = View.VISIBLE
                        }
                    }

                    viewModel.resetStatusUpdateState()
                }
                is TicketDetailsViewModel.StatusUpdateState.Error -> {
                    Toast.makeText(
                        requireContext(),
                        state.message,
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.resetStatusUpdateState()
                }
            }
        }

        viewModel.statuses.observe(viewLifecycleOwner) { statuses ->
            if (statuses.isNotEmpty()) {
                setupStatusDropdown(statuses)
            }
        }

        viewModel.selectedFile.observe(viewLifecycleOwner) { uri ->
            if (uri != null) {
                val fileName = getFileName(uri)
                val isPhoto = fileName.startsWith("TICKET-") && fileName.endsWith(".jpg")

                if (isPhoto) {
                    binding.tvSelectedReplyFile.text = "📷 $fileName"  // __ready_to_send__
                    // Highlight camera button
                    binding.btnTakeReplyPhoto.alpha = 1.0f
                    binding.btnSelectReplyFile.alpha = 0.6f
                } else {
                    binding.tvSelectedReplyFile.text = "📄 $fileName"  // __ready_to_send__
                    // Highlight file button
                    binding.btnSelectReplyFile.alpha = 1.0f
                    binding.btnTakeReplyPhoto.alpha = 0.6f
                }
                binding.tvSelectedReplyFile.visibility = View.VISIBLE
            } else {
                binding.tvSelectedReplyFile.visibility = View.GONE
                // Reset button states
                binding.btnSelectReplyFile.alpha = 1.0f
                binding.btnTakeReplyPhoto.alpha = 1.0f
            }
        }

        viewModel.authorGroups.observe(viewLifecycleOwner) { groups ->
            if (groups.isNotEmpty()) {
                // Join the groups with a comma and display them
                binding.tvAuthorGroups.text = groups.joinToString(", ")
            } else {
                binding.tvAuthorGroups.text = ""
            }
        }

        viewModel.authorFullName.observe(viewLifecycleOwner) { fullName ->
            binding.tvAuthorFullName.text = fullName
        }

        viewModel.ratingState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is TicketDetailsViewModel.RatingState.Idle -> {
                    // Do nothing
                }
                is TicketDetailsViewModel.RatingState.Loading -> {
                    // Loading is handled in the dialog
                }
                is TicketDetailsViewModel.RatingState.Success -> {
                    ratingDialog?.dismiss()
                    ratingDialog = null
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.ticket_rated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.resetRatingState()
                }
                is TicketDetailsViewModel.RatingState.Error -> {
                    Toast.makeText(
                        requireContext(),
                        state.message,
                        Toast.LENGTH_SHORT
                    ).show()
                    viewModel.resetRatingState()
                }
            }
        }
    }

    /**
     * Fetches ticket details from the API.
     */
    private fun fetchTicketDetails() {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.fetchTicketDetails(token, shortUuid!!)
        } else if (token == null) {
            showError("Authentication token not found")
        } else {
            showError("Ticket ID not found")
        }
    }

    /**
     * Displays the ticket details in the UI.
     *
     * @param ticket The ticket to display.
     */
    private fun displayTicketDetails(ticket: Ticket) {
        // Display parent ticket information
        binding.apply {
            tvTicketTitle.text = ticket.title
            tvTicketId.text = ticket.shortUuid
            tvTicketAuthor.text = ticket.author.username
            // We'll set up the category dropdown separately
            // We'll set up the status dropdown separately
            // We'll set up the priority dropdown separately
            ratingBar.rating = ticket.rate.toFloat()

            // Set up rating bar click listener only once
            if (!isRatingClickListenerSetup) {
                setupRatingBarClickListener()
                isRatingClickListenerSetup = true
            }

            tvTicketDate.text = ticket.createdJalali
            HtmlRenderer.applyToTextView(tvTicketMessage, ticket.message)

            // Display status alert message if needed
            when (ticket.status) {
                TicketStatus.CLOSED -> {
                    cardStatusAlert.visibility = View.VISIBLE
                    tvStatusAlertMessage.text = getString(R.string.ticket_closed_message)
                    cardStatusAlert.setCardBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.holo_purple))

                    // Show reply form for closed tickets
                    cardReplyForm.visibility = View.VISIBLE
                }
                TicketStatus.DISABLED -> {
                    cardStatusAlert.visibility = View.VISIBLE
                    tvStatusAlertMessage.text = getString(R.string.ticket_disabled_message)
                    cardStatusAlert.setCardBackgroundColor(ContextCompat.getColor(requireContext(), android.R.color.darker_gray))

                    // Hide reply form for disabled tickets
                    cardReplyForm.visibility = View.GONE
                }
                else -> {
                    cardStatusAlert.visibility = View.GONE

                    // Show reply form for other statuses
                    cardReplyForm.visibility = View.VISIBLE
                }
            }

            // Set up close button for the alert
            btnCloseAlert.setOnClickListener {
                cardStatusAlert.visibility = View.GONE
            }

            // Handle file attachment for parent ticket
            if (ticket.hasFile && ticket.file != null) {
                layoutTicketFile.visibility = View.VISIBLE
                tvTicketFileName.text = ticket.file.substringAfterLast('/')

                // Set up download button click listener
                btnTicketDownloadFile.setOnClickListener {
                    openFileUrl(ticket.file)
                }
            } else {
                layoutTicketFile.visibility = View.GONE
            }
        }

        // Initialize and display ticket responses in chat style
        if (!::chatResponseAdapter.isInitialized) {
            chatResponseAdapter = ChatResponseAdapter(
                viewLifecycleOwner.lifecycleScope,
                ticket.author.username
            )
            binding.recyclerResponses.adapter = chatResponseAdapter
        }
        chatResponseAdapter.submitList(ticket.children)

        // Display ticket changes
        displayTicketChanges(ticket.changes)
    }

    /**
     * Displays the ticket changes in the UI.
     *
     * @param changesMap The map of changes to display.
     */
    private fun displayTicketChanges(changesMap: Map<String, Any>) {
        try {
            val changesList = parseTicketChanges(changesMap)

            if (changesList.isNotEmpty()) {
                // Show the changes section
                binding.tvChangesTitle.visibility = View.VISIBLE
                binding.recyclerChanges.visibility = View.VISIBLE

                // Submit the changes to the adapter
                changeAdapter.submitList(changesList)
            } else {
                // Hide the changes section if no changes
                binding.tvChangesTitle.visibility = View.GONE
                binding.recyclerChanges.visibility = View.GONE
            }
        } catch (e: Exception) {
            // Hide the changes section if parsing fails
            binding.tvChangesTitle.visibility = View.GONE
            binding.recyclerChanges.visibility = View.GONE
            Log.e("TicketDetailsFragment", "Error parsing ticket changes: ${e.message}")
        }
    }

    /**
     * Parses the ticket changes map into a list of TicketChange objects.
     *
     * @param changesMap The map of changes from the API.
     * @return A list of TicketChange objects.
     */
    private fun parseTicketChanges(changesMap: Map<String, Any>): List<TicketChange> {
        val changesList = mutableListOf<TicketChange>()
        val gson = Gson()

        for ((timestamp, changeData) in changesMap) {
            try {
                // Convert the change data to JSON and then parse it as TicketChange
                val changeJson = gson.toJson(changeData)
                val change = gson.fromJson(changeJson, TicketChange::class.java)
                changesList.add(change)
            } catch (e: Exception) {
                Log.e("TicketDetailsFragment", "Error parsing change for timestamp $timestamp: ${e.message}")
            }
        }

        // Sort changes by timestamp (newest first)
        return changesList.sortedByDescending {
            try {
                // Extract timestamp from the map keys if needed for sorting
                // For now, we'll keep the order as is since we don't have timestamp in TicketChange
                0L
            } catch (e: Exception) {
                0L
            }
        }
    }

    /**
     * Extracts the file name from a URL.
     *
     * @param url The URL to extract the file name from.
     * @return The file name.
     */
    private fun getFileNameFromUrl(url: String): String {
        return url.substringAfterLast("/")
    }

    /**
     * Opens a URL in the browser.
     *
     * @param url The URL to open.
     */
    private fun openFileUrl(url: String) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(requireContext(), "خطا در باز کردن فایل: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Shows the loading indicator.
     */
    private fun showLoading() {
        binding.progressBar.visibility = View.VISIBLE
        binding.tvError.visibility = View.GONE
    }

    /**
     * Hides the loading indicator.
     */
    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to display.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    /**
     * Opens the file picker to select a file.
     */
    private fun openFilePicker() {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            addCategory(Intent.CATEGORY_OPENABLE)
            type = "*/*" // Allow all file types
        }
        filePickerLauncher.launch(intent)
    }

    /**
     * Handles the selected file.
     *
     * @param uri The URI of the selected file.
     */
    private fun handleSelectedFile(uri: Uri) {
        try {
            // Check file size (max 5MB)
            val fileSize = getFileSize(uri)
            if (fileSize > 5 * 1024 * 1024) { // 5MB in bytes
                Toast.makeText(requireContext(), getString(R.string.file_too_large), Toast.LENGTH_SHORT).show()
                return
            }

            // Set the selected file in the ViewModel
            viewModel.setSelectedFile(uri)
            selectedFileUri = uri
        } catch (e: Exception) {
            Toast.makeText(requireContext(), getString(R.string.error_selecting_file), Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Gets the file name from a URI.
     *
     * @param uri The URI to get the file name from.
     * @return The file name.
     */
    private fun getFileName(uri: Uri): String {
        var fileName = "unknown_file"

        requireContext().contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val displayNameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (displayNameIndex != -1) {
                    fileName = cursor.getString(displayNameIndex)
                }
            }
        }

        return fileName
    }

    /**
     * Gets the file size from a URI.
     *
     * @param uri The URI to get the file size from.
     * @return The file size in bytes.
     */
    private fun getFileSize(uri: Uri): Long {
        var fileSize = 0L

        requireContext().contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)
                if (sizeIndex != -1) {
                    fileSize = cursor.getLong(sizeIndex)
                }
            }
        }

        return fileSize
    }

    /**
     * Submits the reply to the ticket.
     */
    private fun submitReply() {
        val message = binding.etReplyMessage.text.toString().trim()

        if (!viewModel.validateReplyMessage(message)) {
            binding.tilReplyMessage.error = getString(R.string.please_enter_reply_message)
            return
        }

        binding.tilReplyMessage.error = null

        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            if (selectedFileUri != null) {
                viewModel.replyToTicketWithFile(
                    token,
                    shortUuid!!,
                    message,
                    selectedFileUri!!,
                    requireContext()
                )
            } else {
                viewModel.replyToTicket(token, shortUuid!!, message)
            }
        } else if (token == null) {
            showReplyError("Authentication token not found")
        } else {
            showReplyError("Ticket ID not found")
        }
    }

    /**
     * Shows the reply loading indicator.
     */
    private fun showReplyLoading() {
        binding.progressBarReply.visibility = View.VISIBLE
        binding.btnSubmitReply.isEnabled = false
        binding.btnSelectReplyFile.isEnabled = false
    }

    /**
     * Hides the reply loading indicator.
     */
    private fun hideReplyLoading() {
        binding.progressBarReply.visibility = View.GONE
        binding.btnSubmitReply.isEnabled = true
        binding.btnSelectReplyFile.isEnabled = true
    }

    /**
     * Shows a reply error message.
     *
     * @param message The error message to display.
     */
    private fun showReplyError(message: String) {
        binding.tvReplyError.text = message
        binding.tvReplyError.visibility = View.VISIBLE
    }

    /**
     * Hides the reply error message.
     */
    private fun hideReplyError() {
        binding.tvReplyError.visibility = View.GONE
    }

    /**
     * Clears the reply form.
     */
    private fun clearReplyForm() {
        binding.etReplyMessage.setText("")
        viewModel.setSelectedFile(null)
        selectedFileUri = null
    }

    /**
     * Fetches categories from the API.
     */
    private fun fetchCategories() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchCategories(token)
        }
    }

    /**
     * Fetches priorities from the API.
     */
    private fun fetchPriorities() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchPriorities(token)
        }
    }

    /**
     * Fetches statuses from the API.
     */
    private fun fetchStatuses() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchStatuses(token)
        }
    }

    /**
     * Fetches the groups of a specific user by their short UUID.
     *
     * @param shortUuid The short UUID of the user.
     */
    private fun fetchAuthorGroups(shortUuid: String) {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchAuthorGroups(token, shortUuid)
        }
    }

    /**
     * Fetches the full name of a specific user by their short UUID.
     *
     * @param shortUuid The short UUID of the user.
     */
    private fun fetchAuthorFullName(shortUuid: String) {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchAuthorFullName(token, shortUuid)
        }
    }

    /**
     * Sets up the category dropdown with the list of categories.
     *
     * @param categories The list of categories to display.
     */
    private fun setupCategoryDropdown(categories: List<CategoryResponse>) {
        // Get the current ticket from the ViewModel
        val currentTicket = (viewModel.state.value as? TicketDetailsViewModel.TicketDetailsState.Success)?.ticket

        // Check if the ticket is in a closed or disabled state
        val isTicketClosedOrDisabled = currentTicket?.status?.let { TicketStatus.isDisabledOrClosed(it) } ?: false

        // Create a custom adapter that can disable items
        val adapter = object : ArrayAdapter<String>(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            categories.map { it.persianName }
        ) {
            override fun isEnabled(position: Int): Boolean {
                // If the ticket is closed or disabled, all items in the dropdown should be disabled
                return !isTicketClosedOrDisabled
            }

            override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                val view = super.getView(position, convertView, parent)
                val textView = view.findViewById<TextView>(android.R.id.text1)

                // If the ticket is closed or disabled, make the text appear disabled
                if (isTicketClosedOrDisabled) {
                    textView.setTextColor(ContextCompat.getColor(context, android.R.color.darker_gray))
                } else {
                    // Use theme-aware text color
                    val typedValue = android.util.TypedValue()
                    context.theme.resolveAttribute(android.R.attr.textColorPrimary, typedValue, true)
                    textView.setTextColor(ContextCompat.getColor(context, typedValue.resourceId))
                }

                return view
            }

            override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
                val view = super.getDropDownView(position, convertView, parent)
                val textView = view.findViewById<TextView>(android.R.id.text1)

                // If the ticket is closed or disabled, make the dropdown items appear disabled
                if (isTicketClosedOrDisabled) {
                    textView.setTextColor(ContextCompat.getColor(context, android.R.color.darker_gray))
                } else {
                    // Use theme-aware text color
                    val typedValue = android.util.TypedValue()
                    context.theme.resolveAttribute(android.R.attr.textColorPrimary, typedValue, true)
                    textView.setTextColor(ContextCompat.getColor(context, typedValue.resourceId))
                }

                return view
            }
        }

        binding.dropdownTicketCategory.setAdapter(adapter)

        // Disable the dropdown if the ticket is in a closed or disabled state
        binding.dropdownTicketCategory.isEnabled = !isTicketClosedOrDisabled
        binding.tilTicketCategory.isEnabled = !isTicketClosedOrDisabled

        // If the dropdown is disabled, change its appearance to look disabled
        if (isTicketClosedOrDisabled) {
            binding.dropdownTicketCategory.setTextColor(ContextCompat.getColor(requireContext(), android.R.color.darker_gray))
            binding.tilTicketCategory.boxStrokeColor = ContextCompat.getColor(requireContext(), android.R.color.darker_gray)
            binding.tilTicketCategory.hintTextColor = ContextCompat.getColorStateList(requireContext(), android.R.color.darker_gray)
        }

        // Set the current category as the selected item
        if (currentTicket?.category != null) {
            val currentCategoryPersianName = currentTicket.category.persianName
            val position = categories.indexOfFirst { it.persianName == currentCategoryPersianName }
            if (position != -1) {
                binding.dropdownTicketCategory.setText(currentCategoryPersianName, false)
            }
        }

        // Set up the listener to update the category when the selection changes
        binding.dropdownTicketCategory.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            // Only update if the ticket is not closed or disabled
            if (!isTicketClosedOrDisabled) {
                val selectedCategory = categories[position]
                val selectedCategoryId = selectedCategory.id
                val selectedCategoryName = selectedCategory.persianName

                // Check if the selected category is different from the current one
                // Compare by persianName instead of ID since the ID might be 0 in some cases
                val currentCategoryName = currentTicket?.category?.persianName

                if (currentCategoryName != selectedCategoryName) {
                    // Only update if the selected category is different
                    // Log.d("TicketDetailsFragment", "Selected new category: $selectedCategoryName, current: $currentCategoryName")
                    updateTicketCategory(selectedCategoryId)
                } // else {
                //     Log.d("TicketDetailsFragment", "Selected same category: $selectedCategoryName, skipping update")
                // }
            }
        }
    }

    /**
     * Updates the ticket category.
     *
     * @param categoryId The ID of the new category.
     */
    private fun updateTicketCategory(categoryId: Int) {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            // Get the current ticket
            val currentTicket = (viewModel.state.value as? TicketDetailsViewModel.TicketDetailsState.Success)?.ticket

            // Check if the ticket is in a closed or disabled state
            val isTicketClosedOrDisabled = currentTicket?.status?.let { TicketStatus.isDisabledOrClosed(it) } ?: false

            if (isTicketClosedOrDisabled) {
                // If the ticket is closed or disabled, show a message and don't update
                Toast.makeText(
                    requireContext(),
                    getString(R.string.cannot_update_disabled_ticket),
                    Toast.LENGTH_SHORT
                ).show()
                // Log.d("TicketDetailsFragment", "Cannot update category for closed or disabled ticket: $shortUuid, status: ${currentTicket?.status}")
                return
            }

            // If the ticket is not closed or disabled, proceed with the update
            // Log.d("TicketDetailsFragment", "Updating category to ID: $categoryId for ticket: $shortUuid")
            viewModel.updateTicketCategory(token, shortUuid!!, categoryId)
        } else {
            Log.e("TicketDetailsFragment", "Cannot update category: token=$token, shortUuid=$shortUuid")
        }
    }

    /**
     * Sets up the priority dropdown with the list of priorities.
     *
     * @param priorities The list of priorities to display.
     */
    private fun setupPriorityDropdown(priorities: List<Priority>) {
        // Get the current ticket from the ViewModel
        val currentTicket = (viewModel.state.value as? TicketDetailsViewModel.TicketDetailsState.Success)?.ticket

        // Check if the ticket is in a closed or disabled state
        val isTicketClosedOrDisabled = currentTicket?.status?.let { TicketStatus.isDisabledOrClosed(it) } ?: false

        // Create a custom adapter that can disable items
        val adapter = object : ArrayAdapter<String>(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            priorities.map { it.label }
        ) {
            override fun isEnabled(position: Int): Boolean {
                // If the ticket is closed or disabled, all items in the dropdown should be disabled
                return !isTicketClosedOrDisabled
            }

            override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                val view = super.getView(position, convertView, parent)
                val textView = view.findViewById<TextView>(android.R.id.text1)

                // If the ticket is closed or disabled, make the text appear disabled
                if (isTicketClosedOrDisabled) {
                    textView.setTextColor(ContextCompat.getColor(context, android.R.color.darker_gray))
                } else {
                    // Use theme-aware text color
                    val typedValue = android.util.TypedValue()
                    context.theme.resolveAttribute(android.R.attr.textColorPrimary, typedValue, true)
                    textView.setTextColor(ContextCompat.getColor(context, typedValue.resourceId))
                }

                return view
            }

            override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
                val view = super.getDropDownView(position, convertView, parent)
                val textView = view.findViewById<TextView>(android.R.id.text1)

                // If the ticket is closed or disabled, make the dropdown items appear disabled
                if (isTicketClosedOrDisabled) {
                    textView.setTextColor(ContextCompat.getColor(context, android.R.color.darker_gray))
                } else {
                    // Use theme-aware text color
                    val typedValue = android.util.TypedValue()
                    context.theme.resolveAttribute(android.R.attr.textColorPrimary, typedValue, true)
                    textView.setTextColor(ContextCompat.getColor(context, typedValue.resourceId))
                }

                return view
            }
        }

        binding.dropdownTicketPriority.setAdapter(adapter)

        // Disable the dropdown if the ticket is in a closed or disabled state
        binding.dropdownTicketPriority.isEnabled = !isTicketClosedOrDisabled
        binding.tilTicketPriority.isEnabled = !isTicketClosedOrDisabled

        // If the dropdown is disabled, change its appearance to look disabled
        if (isTicketClosedOrDisabled) {
            binding.dropdownTicketPriority.setTextColor(ContextCompat.getColor(requireContext(), android.R.color.darker_gray))
            binding.tilTicketPriority.boxStrokeColor = ContextCompat.getColor(requireContext(), android.R.color.darker_gray)
            binding.tilTicketPriority.hintTextColor = ContextCompat.getColorStateList(requireContext(), android.R.color.darker_gray)
        }

        // Set the current priority as the selected item
        if (currentTicket != null) {
            val currentPriorityValue = currentTicket.priority
            val currentPriorityLabel = priorities.find { it.value == currentPriorityValue }?.label
            if (currentPriorityLabel != null) {
                binding.dropdownTicketPriority.setText(currentPriorityLabel, false)
            }
        }

        // Set up the listener to update the priority when the selection changes
        binding.dropdownTicketPriority.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            // Only update if the ticket is not closed or disabled
            if (!isTicketClosedOrDisabled) {
                val selectedPriority = priorities[position]
                val selectedPriorityValue = selectedPriority.value
                val selectedPriorityLabel = selectedPriority.label

                // Check if the selected priority is different from the current one
                val currentPriorityValue = currentTicket?.priority

                if (currentPriorityValue != selectedPriorityValue) {
                    // Only update if the selected priority is different
                    // Log.d("TicketDetailsFragment", "Selected new priority: $selectedPriorityLabel ($selectedPriorityValue), current: $currentPriorityValue")
                    updateTicketPriority(selectedPriorityValue)
                } // else {
                //     Log.d("TicketDetailsFragment", "Selected same priority: $selectedPriorityLabel ($selectedPriorityValue), skipping update")
                // }
            }
        }
    }

    /**
     * Updates the ticket priority.
     *
     * @param priorityValue The value of the new priority.
     */
    private fun updateTicketPriority(priorityValue: String) {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            // Get the current ticket
            val currentTicket = (viewModel.state.value as? TicketDetailsViewModel.TicketDetailsState.Success)?.ticket

            // Check if the ticket is in a closed or disabled state
            val isTicketClosedOrDisabled = currentTicket?.status?.let { TicketStatus.isDisabledOrClosed(it) } ?: false

            if (isTicketClosedOrDisabled) {
                // If the ticket is closed or disabled, show a message and don't update
                Toast.makeText(
                    requireContext(),
                    getString(R.string.cannot_update_priority_disabled_ticket),
                    Toast.LENGTH_SHORT
                ).show()
                // Log.d("TicketDetailsFragment", "Cannot update priority for closed or disabled ticket: $shortUuid, status: ${currentTicket?.status}")
                return
            }

            // If the ticket is not closed or disabled, proceed with the update
            // Log.d("TicketDetailsFragment", "Updating priority to value: $priorityValue for ticket: $shortUuid")
            viewModel.updateTicketPriority(token, shortUuid!!, priorityValue)
        } else {
            Log.e("TicketDetailsFragment", "Cannot update priority: token=$token, shortUuid=$shortUuid")
        }
    }

    /**
     * Sets up the status dropdown with the list of statuses.
     *
     * @param statuses The list of statuses to display.
     */
    private fun setupStatusDropdown(statuses: List<Status>) {
        // Get the current ticket from the ViewModel
        val currentTicket = (viewModel.state.value as? TicketDetailsViewModel.TicketDetailsState.Success)?.ticket

        // Check if the ticket is in a closed or disabled state
        val isTicketClosedOrDisabled = currentTicket?.status?.let { TicketStatus.isDisabledOrClosed(it) } ?: false

        // Create a custom adapter that can disable items
        val adapter = object : ArrayAdapter<String>(
            requireContext(),
            android.R.layout.simple_dropdown_item_1line,
            statuses.map { it.label }
        ) {
            override fun isEnabled(position: Int): Boolean {
                // If the ticket is closed or disabled, all items in the dropdown should be disabled
                if (isTicketClosedOrDisabled) {
                    return false
                }

                // Get the status value for this position
                val statusValue = statuses[position].value

                // Disable PENDING and RESPONDED options for all users
                if (statusValue == TicketStatus.PENDING || statusValue == TicketStatus.RESPONDED) {
                    return false
                }

                // Check if the current user is a superuser or limited admin
                val isSuperuserOrLimitedAdmin = sessionManager.getUserIsSuperuser() || sessionManager.getUserIsLimitedAdmin()

                // For non-admin users, disable all options except CLOSED or if the ticket is already DISABLED
                if (!isSuperuserOrLimitedAdmin) {
                    // This is the key logic from the provided rule:
                    // if not option == statusoptions.CLOSED or ticket_object.status == statusoptions.DISABLED:
                    //   ## make the option disabled
                    if (!(statusValue == TicketStatus.CLOSED) || currentTicket?.status == TicketStatus.DISABLED) {
                        return false
                    }
                }

                return true
            }

            override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
                val view = super.getView(position, convertView, parent)
                val textView = view.findViewById<TextView>(android.R.id.text1)

                // Check if this position is enabled
                val isEnabled = isEnabled(position)

                // Set text color based on whether the option is enabled or disabled
                if (isEnabled) {
                    // Use theme-aware text color
                    val typedValue = android.util.TypedValue()
                    context.theme.resolveAttribute(android.R.attr.textColorPrimary, typedValue, true)
                    textView.setTextColor(ContextCompat.getColor(context, typedValue.resourceId))
                } else {
                    textView.setTextColor(ContextCompat.getColor(context, android.R.color.darker_gray))
                }

                return view
            }

            override fun getDropDownView(position: Int, convertView: View?, parent: ViewGroup): View {
                val view = super.getDropDownView(position, convertView, parent)
                val textView = view.findViewById<TextView>(android.R.id.text1)

                // Check if this position is enabled
                val isEnabled = isEnabled(position)

                // Set text color based on whether the option is enabled or disabled
                if (isEnabled) {
                    // Use theme-aware text color
                    val typedValue = android.util.TypedValue()
                    context.theme.resolveAttribute(android.R.attr.textColorPrimary, typedValue, true)
                    textView.setTextColor(ContextCompat.getColor(context, typedValue.resourceId))
                } else {
                    textView.setTextColor(ContextCompat.getColor(context, android.R.color.darker_gray))
                }

                return view
            }
        }

        binding.dropdownTicketStatus.setAdapter(adapter)

        // Disable the dropdown if the ticket is in a closed or disabled state
        binding.dropdownTicketStatus.isEnabled = !isTicketClosedOrDisabled
        binding.tilTicketStatus.isEnabled = !isTicketClosedOrDisabled

        // If the dropdown is disabled, change its appearance to look disabled
        if (isTicketClosedOrDisabled) {
            binding.dropdownTicketStatus.setTextColor(ContextCompat.getColor(requireContext(), android.R.color.darker_gray))
            binding.tilTicketStatus.boxStrokeColor = ContextCompat.getColor(requireContext(), android.R.color.darker_gray)
            binding.tilTicketStatus.hintTextColor = ContextCompat.getColorStateList(requireContext(), android.R.color.darker_gray)
        }

        // Set the current status as the selected item
        if (currentTicket != null) {
            val currentStatusValue = currentTicket.status
            val currentStatusLabel = statuses.find { it.value == currentStatusValue }?.label
            if (currentStatusLabel != null) {
                binding.dropdownTicketStatus.setText(currentStatusLabel, false)
            }
        }

        // Set up the listener to update the status when the selection changes
        binding.dropdownTicketStatus.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            // Only update if the ticket is not closed or disabled
            if (!isTicketClosedOrDisabled) {
                val selectedStatus = statuses[position]
                val selectedStatusValue = selectedStatus.value
                val selectedStatusLabel = selectedStatus.label

                // Check if the selected status is different from the current one
                val currentStatusValue = currentTicket?.status

                if (currentStatusValue != selectedStatusValue) {
                    // Only update if the selected status is different
                    // Log.d("TicketDetailsFragment", "Selected new status: $selectedStatusLabel ($selectedStatusValue), current: $currentStatusValue")
                    updateTicketStatus(selectedStatusValue)
                } // else {
                //     Log.d("TicketDetailsFragment", "Selected same status: $selectedStatusLabel ($selectedStatusValue), skipping update")
                // }
            }
        }
    }

    /**
     * Updates the ticket status.
     *
     * @param statusValue The value of the new status.
     */
    private fun updateTicketStatus(statusValue: String) {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            // Get the current ticket
            val currentTicket = (viewModel.state.value as? TicketDetailsViewModel.TicketDetailsState.Success)?.ticket

            // Check if the ticket is in a closed or disabled state
            val isTicketClosedOrDisabled = currentTicket?.status?.let { TicketStatus.isDisabledOrClosed(it) } ?: false

            if (isTicketClosedOrDisabled) {
                // If the ticket is closed or disabled, show a message and don't update
                Toast.makeText(
                    requireContext(),
                    getString(R.string.cannot_update_status_disabled_ticket),
                    Toast.LENGTH_SHORT
                ).show()
                // Log.d("TicketDetailsFragment", "Cannot update status for closed or disabled ticket: $shortUuid, status: ${currentTicket?.status}")
                return
            }

            // If the ticket is not closed or disabled, proceed with the update
            // Log.d("TicketDetailsFragment", "Updating status to value: $statusValue for ticket: $shortUuid")
            viewModel.updateTicketStatus(token, shortUuid!!, statusValue)
        } else {
            Log.e("TicketDetailsFragment", "Cannot update status: token=$token, shortUuid=$shortUuid")
        }
    }

    /**
     * Sets up the delete button visibility and click listener based on superuser status.
     */
    private fun setupDeleteButton() {
        // Only show the delete button for superusers
        if (sessionManager.getUserIsSuperuser()) {
            binding.btnDeleteTicket.visibility = View.VISIBLE

            // Set up click listener for delete button
            binding.btnDeleteTicket.setOnClickListener {
                showDeleteConfirmationDialog()
            }
        } else {
            binding.btnDeleteTicket.visibility = View.GONE
        }
    }

    /**
     * Shows a confirmation dialog before deleting the ticket.
     */
    private fun showDeleteConfirmationDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_delete_confirm, null)
        val titleTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_title)
        val messageTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_message)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        // Set the title and message
        titleTextView.text = getString(R.string.delete_ticket_confirmation_title)
        messageTextView.text = getString(R.string.delete_ticket_confirmation_message)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            // User confirmed deletion
            deleteTicket()
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * Deletes the ticket.
     */
    private fun deleteTicket() {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.deleteTicket(token, shortUuid!!)
        } else {
            Toast.makeText(
                requireContext(),
                getString(R.string.error_deleting_ticket),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    /**
     * Sets up the rating bar click listener.
     */
    private fun setupRatingBarClickListener() {
        // Check if user is superuser or limited admin
        val isSuperuserOrLimitedAdmin = sessionManager.getUserIsSuperuser() || sessionManager.getUserIsLimitedAdmin()

        if (isSuperuserOrLimitedAdmin) {
            // For superusers and limited admins, disable clicking
            binding.ratingBar.setOnTouchListener { _, _ -> true }
        } else {
            // For regular users, enable clicking and show rating dialog
            binding.ratingBar.setOnTouchListener { _, _ ->
                showRatingDialog()
                true
            }
        }
    }

    /**
     * Shows the rating dialog.
     */
    private fun showRatingDialog() {
        // Prevent multiple dialogs from opening
        if (ratingDialog?.isShowing == true) {
            return
        }

        val dialogBinding = DialogRateTicketBinding.inflate(layoutInflater)
        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogBinding.root)
            .setCancelable(true)
            .create()

        ratingDialog = dialog

        // Set up dialog dismiss listener to clean up reference
        dialog.setOnDismissListener {
            ratingDialog = null
        }

        // Set up the dialog
        dialogBinding.apply {
            // Set up the cancel button
            btnCancel.setOnClickListener {
                dialog.dismiss()
            }

            // Set up the send button
            btnSend.setOnClickListener {
                val rating = ratingBarDialog.rating.toInt()
                if (rating > 0) {
                    // Submit rating and dismiss dialog immediately
                    submitRating(rating)
                    dialog.dismiss()
                } else {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.please_select_a_rating),
                        Toast.LENGTH_SHORT
                    ).show()
                }
            }
        }

        dialog.show()
    }

    /**
     * Submits the rating for the ticket.
     *
     * @param rating The rating value (1-5).
     */
    private fun submitRating(rating: Int) {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.rateTicket(token, shortUuid!!, rating)
        } else {
            Toast.makeText(
                requireContext(),
                getString(R.string.error_rating_ticket),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    /**
     * Checks camera permission and takes a photo.
     */
    private fun checkCameraPermissionAndTakePhoto() {
        when {
            ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                openCamera()
            }
            else -> {
                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    /**
     * Opens the camera to take a photo.
     */
    private fun openCamera() {
        try {
            val photoFile = createImageFile()
            currentPhotoUri = FileProvider.getUriForFile(
                requireContext(),
                "${requireContext().packageName}.fileprovider",
                photoFile
            )
            cameraLauncher.launch(currentPhotoUri)
        } catch (e: Exception) {
            Toast.makeText(
                requireContext(),
                getString(R.string.error_opening_camera),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    /**
     * Creates a temporary image file for the camera.
     */
    private fun createImageFile(): File {
        val timeStamp = SimpleDateFormat("yyyyMMddHHmmss", Locale.US).format(Date())
        val imageFileName = "TICKET-${timeStamp}"
        val storageDir = requireContext().getExternalFilesDir(null)
        return File(storageDir, "${imageFileName}.jpg")
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
