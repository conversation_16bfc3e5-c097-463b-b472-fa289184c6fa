package local.bestoon.data.repository

import local.bestoon.data.api.RetrofitClient
import local.bestoon.data.model.LoginRequest
import local.bestoon.data.model.LoginResponse
import local.bestoon.data.model.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Response

/**
 * Repository class that handles authentication-related operations.
 */
class AuthRepository {
    
    private val apiService = RetrofitClient.getApiService()
    
    /**
     * Attempts to log in a user with the provided credentials.
     *
     * @param username The username for authentication.
     * @param password The password for authentication.
     * @return A Result containing either the LoginResponse or an Exception.
     */
    suspend fun login(username: String, password: String): Result<LoginResponse> {
        return withContext(Dispatchers.IO) {
            try {
                val loginRequest = LoginRequest(username, password)
                val response = apiService.login(loginRequest)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Login failed: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Logs out the user and invalidates the authentication token on the backend.
     *
     * @param token The authentication token.
     * @return A Result indicating success or failure.
     */
    suspend fun logout(token: String): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.logout(authToken)

                if (response.isSuccessful) {
                    Result.success(Unit)
                } else {
                    Result.failure(Exception("خروج ناموفق: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Extracts a User object from a LoginResponse.
     *
     * @param loginResponse The login response containing user information.
     * @return A User object with the user's details.
     */
    fun getUserFromLoginResponse(loginResponse: LoginResponse): User {
        return User(
            id = loginResponse.id,
            username = loginResponse.username,
            isSuperuser = loginResponse.isSuperuser,
            isLimitedAdmin = loginResponse.isLimitedAdmin
        )
    }
}
