<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="2dp"
    android:layout_marginEnd="2dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp"
    app:cardBackgroundColor="@color/card_background"
    app:cardCornerRadius="8dp"
    app:cardElevation="0dp"
    app:strokeWidth="1dp"
    app:strokeColor="@color/card_border">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- First row: created, bank, short_uuid, menu -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/text_created"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="تاریخ ایجاد"
                android:textSize="11sp"
                android:textColor="?android:attr/textColorTertiary" />

            <TextView
                android:id="@+id/text_bank"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="بانک"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:gravity="center"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/text_short_uuid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="UUID"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary" />

            <ImageButton
                android:id="@+id/btn_menu"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_menu_hamburger"
                android:contentDescription="منو"
                android:scaleType="centerInside" />

        </LinearLayout>

        <!-- Second row: title (if not empty) -->
        <TextView
            android:id="@+id/text_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="عنوان"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="?android:attr/textColorPrimary"
            android:layout_marginTop="8dp" />

        <!-- Third row: tags (if not empty) -->
        <TextView
            android:id="@+id/text_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="برچسب‌ها"
            android:textSize="12sp"
            android:textColor="?android:attr/textColorSecondary"
            android:layout_marginTop="4dp" />

        <!-- Bottom row: category and amount -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="4dp">

            <TextView
                android:id="@+id/text_category"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="دسته‌بندی"
                android:textSize="12sp"
                android:textColor="?android:attr/textColorSecondary"
                android:gravity="start" />

            <TextView
                android:id="@+id/text_amount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="مبلغ"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@android:color/holo_green_dark"
                android:gravity="end" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
