package local.bestoon.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing a request to create a new ticket.
 *
 * @property title The title of the ticket.
 * @property message The message content of the ticket.
 * @property category The category of the ticket (persian_name).
 * @property priority The priority of the ticket (value).
 * @property file The file to attach to the ticket (optional).
 */
data class NewTicketRequest(
    val title: String,
    val message: String,
    val category: String,
    val priority: String,
    val file: String? = null
)
