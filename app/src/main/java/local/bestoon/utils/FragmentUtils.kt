package local.bestoon.utils

import android.view.View
import androidx.fragment.app.Fragment
import androidx.navigation.fragment.findNavController
import local.bestoon.data.SessionManager

/**
 * Utility functions for common fragment operations.
 */
object FragmentUtils {

    /**
     * Sets up a FAB button that is only visible to superusers.
     * 
     * @param fragment The fragment context
     * @param fab The FAB view
     * @param navigationAction The navigation action to perform when clicked
     */
    fun setupSuperuserFab(
        fragment: Fragment,
        fab: View,
        navigationAction: Int
    ) {
        val sessionManager = SessionManager(fragment.requireContext())
        val user = sessionManager.getUser()
        
        if (user != null && user.isSuperuser) {
            fab.setOnClickListener {
                try {
                    fragment.findNavController().navigate(navigationAction)
                } catch (e: Exception) {
                    // Handle navigation error silently or log if needed
                }
            }
            fab.visibility = View.VISIBLE
        } else {
            fab.visibility = View.GONE
        }
    }

    /**
     * Checks if the current user is a superuser and shows an error if not.
     * 
     * @param fragment The fragment context
     * @param errorMessage The error message to show
     * @return True if user is superuser, false otherwise
     */
    fun requireSuperuser(
        fragment: Fragment,
        errorMessage: String = "Only administrators can access this feature"
    ): Boolean {
        val sessionManager = SessionManager(fragment.requireContext())
        val user = sessionManager.getUser()
        
        if (user == null || !user.isSuperuser) {
            android.widget.Toast.makeText(
                fragment.requireContext(),
                errorMessage,
                android.widget.Toast.LENGTH_SHORT
            ).show()
            fragment.findNavController().navigateUp()
            return false
        }
        return true
    }
}
