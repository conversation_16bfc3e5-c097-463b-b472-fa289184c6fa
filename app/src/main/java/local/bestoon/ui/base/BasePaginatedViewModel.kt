package local.bestoon.ui.base

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import local.bestoon.data.model.PaginatedResponse
import kotlinx.coroutines.launch

/**
 * Base ViewModel for handling paginated data with common loading states and error handling.
 */
abstract class BasePaginatedViewModel<T> : ViewModel() {

    private val _items = MutableLiveData<List<T>>()
    val items: LiveData<List<T>> = _items

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isLoadingMore = MutableLiveData<Boolean>(false)
    val isLoadingMore: LiveData<Boolean> = _isLoadingMore

    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    protected var nextPageUrl: String? = null
    protected var currentItems = mutableListOf<T>()

    /**
     * Abstract method to fetch the first page of items.
     * Implementations should call the appropriate repository method.
     */
    protected abstract suspend fun fetchFirstPage(token: String, pageSize: Int): Result<PaginatedResponse<T>>

    /**
     * Abstract method to fetch items from a specific URL.
     * Implementations should call the appropriate repository method.
     */
    protected abstract suspend fun fetchFromUrl(token: String, url: String): Result<PaginatedResponse<T>>

    /**
     * Fetches the first page of items.
     */
    fun fetchItems(token: String, pageSize: Int = 15) {
        _isLoading.value = true
        _error.value = null
        currentItems.clear()

        viewModelScope.launch {
            try {
                val result = fetchFirstPage(token, pageSize)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentItems.addAll(paginatedResponse.results)
                        _items.value = currentItems.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Loads more items from the next page if available.
     */
    fun loadMoreItems(token: String) {
        if (nextPageUrl == null || _isLoadingMore.value == true) {
            return
        }

        _isLoadingMore.value = true

        viewModelScope.launch {
            try {
                val result = fetchFromUrl(token, nextPageUrl!!)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentItems.addAll(paginatedResponse.results)
                        _items.value = currentItems.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoadingMore.value = false
            }
        }
    }

    /**
     * Checks if there are more pages to load.
     */
    fun hasMorePages(): Boolean {
        return nextPageUrl != null
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }
}
