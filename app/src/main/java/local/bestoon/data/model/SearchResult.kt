package local.bestoon.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * Data class representing the search results from the API.
 *
 * @property matchedTransactions List of transactions matching the search query.
 * @property matchedEvents List of events matching the search query.
 */
data class SearchResult(
    @SerializedName("matched_transactions") val matchedTransactions: List<SearchTransaction>,
    @SerializedName("matched_events") val matchedEvents: List<SearchEvent>
)

/**
 * Data class representing a transaction in search results.
 */
@Parcelize
data class SearchTransaction(
    val id: Int,
    val mode: String,
    val title: String?,
    val author: Author?,
    val amount: Int,
    val year: Int,
    val month: Int,
    val day: Int,
    @SerializedName("slashed_date")
    val slashedDate: String,
    @SerializedName("slashed_date_persian")
    val slashedDatePersian: String,
    @SerializedName("numerical_date")
    val numericalDate: String,
    @SerializedName("bank_info")
    val bankInfo: Bank?,
    @SerializedName("category_info")
    val categoryInfo: HomepageCategory?,
    @SerializedName("tags_names")
    val tagsNames: List<String>,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val active: Boolean,
    val created: String,
    val updated: String
) : Parcelable

/**
 * Data class representing an event in search results.
 */
@Parcelize
data class SearchEvent(
    val id: Int,
    val title: String,
    val author: Author?,
    val year: Int,
    val month: Int,
    val day: Int,
    @SerializedName("slashed_date")
    val slashedDate: String,
    @SerializedName("slashed_date_persian")
    val slashedDatePersian: String,
    @SerializedName("numerical_date")
    val numericalDate: String,
    val active: Boolean,
    @SerializedName("short_uuid")
    val shortUuid: String,
    val created: String,
    val updated: String
) : Parcelable
