<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/colorSurface"
    tools:context=".ui.reports.ReportsFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="6dp"
        android:clipToPadding="false">

        <!-- Filter Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp"
            android:gravity="center_vertical">

            <!-- Year Select (Right) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/year_input_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="سال">

                <AutoCompleteTextView
                    android:id="@+id/year_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAlignment="center" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Month Start Select (Middle) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/month_start_input_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="از">

                <AutoCompleteTextView
                    android:id="@+id/month_start_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAlignment="center" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Month End Select (Left) -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/month_end_input_layout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="تا">

                <AutoCompleteTextView
                    android:id="@+id/month_end_spinner"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none"
                    android:textAlignment="center" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Cards Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:clipChildren="false"
            android:clipToPadding="false">

            <!-- First Row: Income Year and Income Month -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:clipChildren="false"
                android:clipToPadding="false">

                <!-- Card 1: Income Year (Right) -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_income_year"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="4dp"
                    app:cardBackgroundColor="@color/card_background"
                    app:cardCornerRadius="8dp"
                    app:cardUseCompatPadding="true"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/card_border">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- Header Row -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="درآمد"
                                android:textColor="@android:color/holo_green_dark"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/text_income_year_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۱۴۰۴"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <!-- Data Rows -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="بیشترین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_income_year_max"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۱،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="کمترین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_income_year_min"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۲،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="میانگین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_income_year_avg"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۳،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="مجموع"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_income_year_total"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۴،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Card 2: Income Month (Left) -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_income_month"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    app:cardBackgroundColor="@color/card_background"
                    app:cardCornerRadius="8dp"
                    app:cardUseCompatPadding="true"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/card_border">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- Header Row -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="درآمد"
                                android:textColor="@android:color/holo_green_dark"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/text_income_month_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۱۴۰۴/۰۳"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <!-- Data Rows -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="بیشترین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_income_month_max"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۱،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="کمترین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_income_month_min"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۲،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="میانگین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_income_month_avg"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۳،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="مجموع"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_income_month_total"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۴،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <!-- Second Row: Expenditure Year and Expenditure Month -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:clipChildren="false"
                android:clipToPadding="false">

                <!-- Card 3: Expenditure Year (Right) -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_expenditure_year"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="4dp"
                    app:cardBackgroundColor="@color/card_background"
                    app:cardCornerRadius="8dp"
                    app:cardUseCompatPadding="true"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/card_border">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- Header Row -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="هزینه"
                                android:textColor="@android:color/holo_red_dark"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/text_expenditure_year_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۱۴۰۴"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <!-- Data Rows -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="بیشترین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_expenditure_year_max"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۱،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="کمترین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_expenditure_year_min"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۲،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="میانگین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_expenditure_year_avg"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۳،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="مجموع"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_expenditure_year_total"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۴،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Card 4: Expenditure Month (Left) -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/card_expenditure_month"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="4dp"
                    app:cardBackgroundColor="@color/card_background"
                    app:cardCornerRadius="8dp"
                    app:cardUseCompatPadding="true"
                    app:cardElevation="0dp"
                    app:strokeWidth="1dp"
                    app:strokeColor="@color/card_border">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <!-- Header Row -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="هزینه"
                                android:textColor="@android:color/holo_red_dark"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/text_expenditure_month_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۱۴۰۴/۰۳"
                                android:textSize="12sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <!-- Data Rows -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="بیشترین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_expenditure_month_max"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۱،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="کمترین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_expenditure_month_min"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۲،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="4dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="میانگین"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_expenditure_month_avg"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۳،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="مجموع"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/text_expenditure_month_total"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="۴،۰۰۰"
                                android:textSize="12sp" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </LinearLayout>

        <!-- Loading and Error States -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="32dp"
            android:visibility="gone" />

        <TextView
            android:id="@+id/error_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="خطا در بارگذاری اطلاعات"
            android:textSize="16sp"
            android:visibility="gone"
            android:layout_margin="16dp" />

    </LinearLayout>

</ScrollView>
