package local.bestoon.utils

/**
 * Utility class for handling authentication tokens.
 */
object TokenUtils {
    
    /**
     * Formats a token for API requests by adding the "Token " prefix if not already present.
     * 
     * @param token The raw token string
     * @return The formatted token string with "Token " prefix
     */
    fun formatToken(token: String): String {
        return if (token.startsWith("Token ")) {
            token
        } else {
            "Token $token"
        }
    }
    
    /**
     * Validates if a token is not null or empty.
     * 
     * @param token The token to validate
     * @return True if token is valid, false otherwise
     */
    fun isValidToken(token: String?): Bo<PERSON>an {
        return !token.isNullOrBlank()
    }
}
