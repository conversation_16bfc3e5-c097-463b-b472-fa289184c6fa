<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="6dp"
    android:background="?attr/colorSurface"
    tools:context=".ui.home.HomeFragment">

    <!-- Filter Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="12dp"
        android:gravity="center_vertical">

        <!-- Year Select (Right) -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/year_input_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:hint="سال">

            <AutoCompleteTextView
                android:id="@+id/year_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"
                android:textAlignment="center" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Month Start Select (Middle) -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/month_start_input_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:hint="از">

            <AutoCompleteTextView
                android:id="@+id/month_start_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"
                android:textAlignment="center" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Month End Select (Left) -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/month_end_input_layout"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="تا">

            <AutoCompleteTextView
                android:id="@+id/month_end_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none"
                android:textAlignment="center" />

        </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

    <!-- Tabs Section -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        app:tabMode="fixed"
        app:tabGravity="fill" />

    <!-- ViewPager for Tab Content -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/view_pager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:minHeight="400dp" />

    <!-- Loading and Error States -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <TextView
        android:id="@+id/error_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="خطا در بارگذاری اطلاعات"
        android:textSize="16sp"
        android:visibility="gone"
        android:layout_margin="16dp" />

</LinearLayout>
