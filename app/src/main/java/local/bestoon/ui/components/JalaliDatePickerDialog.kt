package local.bestoon.ui.components

import android.app.Dialog
import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.widget.Button
import android.widget.NumberPicker
import local.bestoon.R
import local.bestoon.utils.JalaliDateUtils
import local.bestoon.utils.PersianUtils
import java.util.Calendar

/**
 * Custom Jalali date picker dialog.
 */
class JalaliDatePickerDialog(
    context: Context,
    private val onDateSelected: (year: Int, month: Int, day: Int) -> Unit,
    initialYear: Int? = null,
    initialMonth: Int? = null,
    initialDay: Int? = null
) : Dialog(context) {

    private val currentJalali: IntArray
    private val initialYear: Int
    private val initialMonth: Int
    private val initialDay: Int

    private lateinit var yearPicker: NumberPicker
    private lateinit var monthPicker: NumberPicker
    private lateinit var dayPicker: NumberPicker
    private lateinit var btnCancel: Button
    private lateinit var btnOk: Button

    init {
        // Get current Jalali date
        val calendar = Calendar.getInstance()
        currentJalali = JalaliDateUtils.gregorianToJalali(
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH) + 1,
            calendar.get(Calendar.DAY_OF_MONTH)
        )

        this.initialYear = initialYear ?: currentJalali[0]
        this.initialMonth = initialMonth ?: currentJalali[1]
        this.initialDay = initialDay ?: currentJalali[2]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_jalali_date_picker, null)
        setContentView(view)

        // Initialize views
        yearPicker = view.findViewById(R.id.year_picker)
        monthPicker = view.findViewById(R.id.month_picker)
        dayPicker = view.findViewById(R.id.day_picker)
        btnCancel = view.findViewById(R.id.btn_cancel)
        btnOk = view.findViewById(R.id.btn_ok)

        setupDatePickers()
        setupButtons()
        
        // Set dialog properties
        window?.setLayout(
            (context.resources.displayMetrics.widthPixels * 0.9).toInt(),
            android.view.ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    private fun setupDatePickers() {
        // Setup year picker
        val currentYear = currentJalali[0]
        val yearRange = (currentYear - 10)..(currentYear + 10)

        yearPicker.apply {
            minValue = 0
            maxValue = yearRange.count() - 1
            displayedValues = yearRange.map { PersianUtils.convertToPersianNumerals(it) }.toTypedArray()
            // Find the index of initialYear, default to current year if not found
            val yearIndex = yearRange.indexOf(initialYear)
            value = if (yearIndex >= 0) yearIndex else yearRange.indexOf(currentYear)
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }

        // Setup month picker
        val monthNames = (1..12).map { JalaliDateUtils.getPersianMonthName(it) }.toTypedArray()

        monthPicker.apply {
            minValue = 0
            maxValue = 11
            displayedValues = monthNames
            // Ensure the initial month is within valid bounds (1-12)
            val validInitialMonth = initialMonth.coerceIn(1, 12)
            value = validInitialMonth - 1
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }

        // Setup day picker
        updateDayPicker()

        // Add listeners to update day picker when year or month changes
        yearPicker.setOnValueChangedListener { _, _, _ -> updateDayPicker() }
        monthPicker.setOnValueChangedListener { _, _, _ -> updateDayPicker() }
    }

    private fun updateDayPicker() {
        val selectedYear = getSelectedYear()
        val selectedMonth = getSelectedMonth()
        val daysInMonth = JalaliDateUtils.getDaysInJalaliMonth(selectedYear, selectedMonth)

        // Get current day, but handle case where dayPicker might not be initialized
        val currentDay = try {
            dayPicker.value + 1
        } catch (e: Exception) {
            // Use the actual initial day, not just coerced to month bounds
            initialDay
        }

        dayPicker.apply {
            minValue = 0
            maxValue = daysInMonth - 1
            displayedValues = (1..daysInMonth).map { PersianUtils.convertToPersianNumerals(it) }.toTypedArray()

            // Preserve current day if possible, otherwise set to last day of month
            val validDay = currentDay.coerceIn(1, daysInMonth)
            value = validDay - 1
            descendantFocusability = NumberPicker.FOCUS_BLOCK_DESCENDANTS
        }
    }

    private fun setupButtons() {
        btnCancel.setOnClickListener {
            dismiss()
        }

        btnOk.setOnClickListener {
            val year = getSelectedYear()
            val month = getSelectedMonth()
            val day = getSelectedDay()
            
            onDateSelected(year, month, day)
            dismiss()
        }
    }

    private fun getSelectedYear(): Int {
        val currentYear = currentJalali[0]
        val yearRange = (currentYear - 10)..(currentYear + 10)
        return yearRange.elementAt(yearPicker.value)
    }

    private fun getSelectedMonth(): Int {
        return monthPicker.value + 1
    }

    private fun getSelectedDay(): Int {
        return dayPicker.value + 1
    }
}
