package local.bestoon.ui.tickets

import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import local.bestoon.R
import local.bestoon.data.model.Ticket
import local.bestoon.databinding.ItemTicketBinding

/**
 * Adapter for displaying tickets in a RecyclerView.
 *
 * @property onTicketClick Callback for when a ticket is clicked.
 */
class TicketAdapter(private val onTicketClick: (Ticket) -> Unit) :
    ListAdapter<Ticket, TicketAdapter.TicketViewHolder>(TicketDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TicketViewHolder {
        val binding = ItemTicketBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TicketViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TicketViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder for a ticket item.
     */
    inner class TicketViewHolder(private val binding: ItemTicketBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onTicketClick(getItem(position))
                }
            }
        }

        /**
         * Binds a ticket to the ViewHolder.
         *
         * @param ticket The ticket to bind.
         */
        fun bind(ticket: Ticket) {
            val context = binding.root.context

            binding.apply {
                tvTicketTitle.text = ticket.title
                // Handle category safely
                chipCategory.text = ticket.category?.name ?: context.getString(R.string.category_other)
                // Display status_persian and priority_persian (use statusDisplay/priorityDisplay or codes as fallbacks)
                chipStatus.text = ticket.statusPersian ?: ticket.statusDisplay ?: ticket.status ?: ""
                chipPriority.text = ticket.priorityPersian ?: ticket.priorityDisplay ?: ticket.priority
                // Display just the ticket ID without label
                tvTicketId.text = ticket.shortUuid
                // Display just the author's username without label
                tvTicketAuthor.text = ticket.author.username
                tvCreatedDate.text = ticket.createdJalali

                // We're setting these in the layout XML now
                // No need to set chipMinHeight and chipCornerRadius programmatically

                // Set category chip colors
                chipCategory.setTextColor(ContextCompat.getColor(context, R.color.category_fg))
                chipCategory.chipBackgroundColor = ColorStateList.valueOf(
                    ContextCompat.getColor(context, R.color.category_bg)
                )

                // Set status chip colors based on status
                when (ticket.status) {
                    "P" -> {
                        chipStatus.setTextColor(ContextCompat.getColor(context, R.color.status_p_fg))
                        chipStatus.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.status_p_bg)
                        )
                    }
                    "I" -> {
                        chipStatus.setTextColor(ContextCompat.getColor(context, R.color.status_i_fg))
                        chipStatus.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.status_i_bg)
                        )
                    }
                    "R" -> {
                        chipStatus.setTextColor(ContextCompat.getColor(context, R.color.status_r_fg))
                        chipStatus.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.status_r_bg)
                        )
                    }
                    "C" -> {
                        chipStatus.setTextColor(ContextCompat.getColor(context, R.color.status_c_fg))
                        chipStatus.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.status_c_bg)
                        )
                    }
                    "D" -> {
                        chipStatus.setTextColor(ContextCompat.getColor(context, R.color.status_d_fg))
                        chipStatus.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.status_d_bg)
                        )
                    }
                    else -> {
                        // Default case
                        chipStatus.setTextColor(ContextCompat.getColor(context, R.color.status_d_fg))
                        chipStatus.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.status_d_bg)
                        )
                    }
                }

                // Set priority chip colors based on priority
                when (ticket.priority) {
                    "VH" -> {
                        chipPriority.setTextColor(ContextCompat.getColor(context, R.color.priority_vh_fg))
                        chipPriority.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.priority_vh_bg)
                        )
                    }
                    "H" -> {
                        chipPriority.setTextColor(ContextCompat.getColor(context, R.color.priority_h_fg))
                        chipPriority.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.priority_h_bg)
                        )
                    }
                    "M" -> {
                        chipPriority.setTextColor(ContextCompat.getColor(context, R.color.priority_m_fg))
                        chipPriority.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.priority_m_bg)
                        )
                    }
                    "L" -> {
                        chipPriority.setTextColor(ContextCompat.getColor(context, R.color.priority_l_fg))
                        chipPriority.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.priority_l_bg)
                        )
                    }
                    "VL" -> {
                        chipPriority.setTextColor(ContextCompat.getColor(context, R.color.priority_vl_fg))
                        chipPriority.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.priority_vl_bg)
                        )
                    }
                    else -> {
                        // Default case
                        chipPriority.setTextColor(ContextCompat.getColor(context, R.color.priority_m_fg))
                        chipPriority.chipBackgroundColor = ColorStateList.valueOf(
                            ContextCompat.getColor(context, R.color.priority_m_bg)
                        )
                    }
                }
            }
        }
    }

    /**
     * DiffUtil callback for comparing tickets.
     */
    private class TicketDiffCallback : DiffUtil.ItemCallback<Ticket>() {
        override fun areItemsTheSame(oldItem: Ticket, newItem: Ticket): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Ticket, newItem: Ticket): Boolean {
            return oldItem == newItem
        }
    }
}
