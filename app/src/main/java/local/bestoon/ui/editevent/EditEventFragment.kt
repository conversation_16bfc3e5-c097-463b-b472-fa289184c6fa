package local.bestoon.ui.editevent

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import local.bestoon.BestoonApplication
import local.bestoon.R
import local.bestoon.data.SessionManager
import local.bestoon.data.model.EventObject
import local.bestoon.databinding.FragmentEditEventBinding
import local.bestoon.ui.components.JalaliDatePickerDialog
import local.bestoon.utils.JalaliDateUtils
import local.bestoon.utils.PersianUtils

class EditEventFragment : Fragment() {

    private var _binding: FragmentEditEventBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EditEventViewModel
    private lateinit var sessionManager: SessionManager

    private var shortUuid: String? = null
    private var eventObject: EventObject? = null
    private var selectedDate: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Get arguments
        arguments?.let {
            shortUuid = it.getString("shortUuid")
            eventObject = it.getParcelable("eventObject")
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentEditEventBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = (requireActivity().application as BestoonApplication).sessionManager
        
        val token = sessionManager.getAuthToken()
        if (token.isNullOrEmpty()) {
            Toast.makeText(
                requireContext(),
                getString(R.string.authentication_token_not_found),
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        // Check if we have the event data
        if (eventObject == null) {
            Toast.makeText(
                requireContext(),
                "Event data not found",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        val factory = EditEventViewModelFactory(token)
        viewModel = ViewModelProvider(this, factory)[EditEventViewModel::class.java]

        populateForm()
        setupListeners()
        observeViewModel()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    /**
     * Populates the form with the event data.
     */
    private fun populateForm() {
        eventObject?.let { event ->
            // Set title
            binding.etEventTitle.setText(event.title)

            // Set date
            selectedDate = JalaliDateUtils.formatJalaliDate(event.year, event.month, event.day)
            val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
            binding.etEventDate.setText(displayDate)
        }
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        // Date picker
        binding.etEventDate.setOnClickListener {
            showDatePicker()
        }

        // Submit button
        binding.btnSubmit.setOnClickListener {
            submitEvent()
        }
    }

    /**
     * Shows the Jalali date picker dialog.
     */
    private fun showDatePicker() {
        val jalaliDatePickerDialog = JalaliDatePickerDialog(
            requireContext(),
            { year, month, day ->
                // Format the selected Jalali date
                selectedDate = JalaliDateUtils.formatJalaliDate(year, month, day)

                // Display the date with Persian numerals
                val displayDate = PersianUtils.convertToPersianNumerals(selectedDate!!)
                binding.etEventDate.setText(displayDate)
                binding.tilEventDate.error = null
            }
        )
        jalaliDatePickerDialog.show()
    }

    /**
     * Validates and submits the event form.
     */
    private fun submitEvent() {
        val title = binding.etEventTitle.text.toString().trim()
        
        // Clear previous errors
        binding.tilEventTitle.error = null
        binding.tilEventDate.error = null

        var hasError = false

        // Validate title
        if (title.isEmpty()) {
            binding.tilEventTitle.error = getString(R.string.please_enter_title)
            hasError = true
        }

        // Validate date
        if (selectedDate.isNullOrEmpty()) {
            binding.tilEventDate.error = getString(R.string.please_select_date)
            hasError = true
        }

        if (!hasError) {
            // Update the event with English numerals for the date
            val token = sessionManager.getAuthToken()
            if (token != null && shortUuid != null) {
                // Ensure the date contains English numerals before sending to API
                val dateWithEnglishNumerals = PersianUtils.convertToEnglishNumerals(selectedDate!!)
                viewModel.updateEvent(
                    token = token,
                    shortUuid = shortUuid!!,
                    title = title,
                    date = dateWithEnglishNumerals
                )
            } else if (token == null) {
                showError(getString(R.string.authentication_token_not_found))
            } else {
                showError("Event ID not found")
            }
        }
    }

    /**
     * Observes ViewModel LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
        }

        viewModel.eventUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is EditEventViewModel.EventUpdateResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.event_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate back to home
                    findNavController().navigate(R.id.nav_home)
                }
                is EditEventViewModel.EventUpdateResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Shows an error message to the user.
     */
    private fun showError(message: String) {
        Toast.makeText(requireContext(), message, Toast.LENGTH_LONG).show()
    }
}
