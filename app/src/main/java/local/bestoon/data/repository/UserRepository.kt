package local.bestoon.data.repository

import android.util.Log
import local.bestoon.data.api.RetrofitClient
import local.bestoon.data.model.UserListItem
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository class that handles user-related operations.
 */
class UserRepository {

    private val apiService = RetrofitClient.getApiService()
    private val TAG = "UserRepository"

    /**
     * Retrieves the list of all users.
     *
     * @param token The authentication token.
     * @return A Result containing either the list of users or an Exception.
     */
    suspend fun getUsers(token: String): Result<List<UserListItem>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getUsers(authToken)

                if (response.isSuccessful && response.body() != null) {
                    // Extract the results from the paginated response
                    Result.success(response.body()!!.results)
                } else {
                    Result.failure(Exception("Failed to fetch users: ${response.message()}"))
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching users", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the categories of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Result containing either the list of categories or an Exception.
     */
    suspend fun getUserCategories(token: String, shortUuid: String): Result<List<String>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                // Log.d(TAG, "Fetching categories for user: $shortUuid")

                val response = apiService.getUserCategories(authToken, shortUuid)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch user categories: ${response.message()}"))
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching user categories", e)
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the groups of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Result containing either the list of groups or an Exception.
     */
    suspend fun getUserGroups(token: String, shortUuid: String): Result<List<String>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                // Log.d(TAG, "Fetching groups for user: $shortUuid")

                val response = apiService.getUserGroups(authToken, shortUuid)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch user groups: ${response.message()}"))
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error fetching user groups", e)
                Result.failure(e)
            }
        }
    }
}
