<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.CAMERA" />

    <!-- __BY_ME__ -->
    <!--   1. replaced true with ${BACKUP_IS_ALLOWED} in this block -->
    <!--      (BACKUP_IS_ALLOWED is defined in app/build.gradle.kts) -->
    <!--   2. replaced true with ${USES_CLEAR_TEXT_TRAFFIC} in this block -->
    <!--      (USES_CLEAR_TEXT_TRAFFIC is defined in app/build.gradle.kts) -->
    <application
        android:name=".BestoonApplication"
        android:allowBackup="${BACKUP_IS_ALLOWED}"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.Bestoon"
        android:usesCleartextTraffic="${USES_CLEAR_TEXT_TRAFFIC}"
        tools:targetApi="31">
        <activity
            android:name=".ui.login.LoginActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.Bestoon.NoActionBar">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.Bestoon.NoActionBar" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>
