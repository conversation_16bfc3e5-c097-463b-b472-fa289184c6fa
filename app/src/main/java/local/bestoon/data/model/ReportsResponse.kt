package local.bestoon.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing a report data for income or expenditure.
 */
data class ReportData(
    @SerializedName("بیشترین")
    val maximum: Int,
    @SerializedName("کمترین")
    val minimum: Int,
    @SerializedName("میانگین")
    val average: Int,
    @SerializedName("مجموع")
    val total: Int
)

/**
 * Data class representing the reports response from the API.
 */
data class ReportsResponse(
    val years: List<Int>,
    @SerializedName("names_of_months__persian")
    val namesOfMonthsPersian: List<String>,
    val chosenyear: Int,
    val chosenmonthstart: Int,
    val chosenmonthend: Int,
    @SerializedName("chosenmonthstart__zeroed")
    val chosenmonthstartZeroed: String,
    @SerializedName("chosenmonthend__zeroed")
    val chosenmonthendZeroed: String,
    @SerializedName("chosenyear__persian")
    val chosenyearPersian: String,
    @SerializedName("chosenmonthstart__zeroed__persian")
    val chosenmonthstartZeroedPersian: String,
    @SerializedName("chosenmonthend__zeroed__persian")
    val chosenmonthendZeroedPersian: String,
    @SerializedName("expenditure_report__year")
    val expenditureReportYear: ReportData,
    @SerializedName("income_report__year")
    val incomeReportYear: ReportData,
    @SerializedName("expenditure_report__month")
    val expenditureReportMonth: ReportData,
    @SerializedName("income_report__month")
    val incomeReportMonth: ReportData
)
