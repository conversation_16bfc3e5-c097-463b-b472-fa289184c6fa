package local.bestoon.utils

import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import local.bestoon.BuildConfig
import local.bestoon.R

/**
 * Utility class for handling biometric authentication using BiometricPrompt.
 * Supports fingerprint, face recognition, and device credentials (PIN, pattern, password).
 */
class BiometricAuthManager(private val activity: FragmentActivity) {

    /**
     * Interface for handling biometric authentication results.
     */
    interface BiometricAuthCallback {
        /**
         * Called when authentication succeeds.
         */
        fun onAuthenticationSucceeded()

        /**
         * Called when authentication fails or is cancelled.
         *
         * @param errorMessage The error message describing what went wrong.
         */
        fun onAuthenticationError(errorMessage: String)
    }

    /**
     * Checks if biometric authentication is available on the device.
     *
     * @return BiometricAvailability indicating the availability status.
     */
    fun checkBiometricAvailability(): BiometricAvailability {
        val biometricManager = BiometricManager.from(activity)

        val result = biometricManager.canAuthenticate(
            BiometricManager.Authenticators.BIOMETRIC_STRONG or
            BiometricManager.Authenticators.DEVICE_CREDENTIAL
        )

        // Debug logging (only in debug builds)
        if (BuildConfig.DEBUG) {
            android.util.Log.d("BiometricAuthManager", "canAuthenticate result: $result")
        }

        return when (result) {
            BiometricManager.BIOMETRIC_SUCCESS -> {
                if (BuildConfig.DEBUG) {
                    android.util.Log.d("BiometricAuthManager", "Biometric authentication is available")
                }
                BiometricAvailability.AVAILABLE
            }
            BiometricManager.BIOMETRIC_ERROR_NO_HARDWARE -> {
                if (BuildConfig.DEBUG) {
                    android.util.Log.d("BiometricAuthManager", "No biometric hardware")
                }
                BiometricAvailability.NO_HARDWARE
            }
            BiometricManager.BIOMETRIC_ERROR_HW_UNAVAILABLE -> {
                if (BuildConfig.DEBUG) {
                    android.util.Log.d("BiometricAuthManager", "Biometric hardware unavailable")
                }
                BiometricAvailability.HARDWARE_UNAVAILABLE
            }
            BiometricManager.BIOMETRIC_ERROR_NONE_ENROLLED -> {
                if (BuildConfig.DEBUG) {
                    android.util.Log.d("BiometricAuthManager", "No biometric credentials enrolled")
                }
                BiometricAvailability.NONE_ENROLLED
            }
            BiometricManager.BIOMETRIC_ERROR_SECURITY_UPDATE_REQUIRED -> {
                if (BuildConfig.DEBUG) {
                    android.util.Log.d("BiometricAuthManager", "Security update required")
                }
                BiometricAvailability.SECURITY_UPDATE_REQUIRED
            }
            BiometricManager.BIOMETRIC_ERROR_UNSUPPORTED -> {
                if (BuildConfig.DEBUG) {
                    android.util.Log.d("BiometricAuthManager", "Biometric authentication unsupported")
                }
                BiometricAvailability.UNSUPPORTED
            }
            BiometricManager.BIOMETRIC_STATUS_UNKNOWN -> {
                if (BuildConfig.DEBUG) {
                    android.util.Log.d("BiometricAuthManager", "Biometric status unknown")
                }
                BiometricAvailability.UNKNOWN
            }
            else -> {
                if (BuildConfig.DEBUG) {
                    android.util.Log.d("BiometricAuthManager", "Unknown result: $result")
                }
                BiometricAvailability.UNKNOWN
            }
        }
    }

    /**
     * Initiates biometric authentication.
     *
     * @param title The title to display in the biometric prompt.
     * @param subtitle The subtitle to display in the biometric prompt.
     * @param callback The callback to handle authentication results.
     */
    fun authenticate(
        title: String = activity.getString(R.string.biometric_title),
        subtitle: String = activity.getString(R.string.biometric_subtitle),
        callback: BiometricAuthCallback
    ) {
        val availability = checkBiometricAvailability()
        if (availability != BiometricAvailability.AVAILABLE) {
            callback.onAuthenticationError(getAvailabilityErrorMessage(availability))
            return
        }

        val biometricPrompt = BiometricPrompt(
            activity,
            ContextCompat.getMainExecutor(activity),
            object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    super.onAuthenticationSucceeded(result)
                    callback.onAuthenticationSucceeded()
                }

                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    super.onAuthenticationError(errorCode, errString)
                    callback.onAuthenticationError(errString.toString())
                }

                override fun onAuthenticationFailed() {
                    super.onAuthenticationFailed()
                    callback.onAuthenticationError(activity.getString(R.string.biometric_authentication_failed))
                }
            }
        )

        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle(title)
            .setSubtitle(subtitle)
            .setAllowedAuthenticators(
                BiometricManager.Authenticators.BIOMETRIC_STRONG or
                BiometricManager.Authenticators.DEVICE_CREDENTIAL
            )
            .build()

        biometricPrompt.authenticate(promptInfo)
    }

    /**
     * Gets a user-friendly error message for the given availability status.
     */
    private fun getAvailabilityErrorMessage(availability: BiometricAvailability): String {
        return when (availability) {
            BiometricAvailability.NO_HARDWARE -> activity.getString(R.string.biometric_no_hardware)
            BiometricAvailability.HARDWARE_UNAVAILABLE -> activity.getString(R.string.biometric_hardware_unavailable)
            BiometricAvailability.NONE_ENROLLED -> activity.getString(R.string.biometric_none_enrolled)
            BiometricAvailability.SECURITY_UPDATE_REQUIRED -> activity.getString(R.string.biometric_security_update_required)
            BiometricAvailability.UNSUPPORTED -> activity.getString(R.string.biometric_unsupported)
            BiometricAvailability.UNKNOWN -> activity.getString(R.string.biometric_unknown_error)
            BiometricAvailability.AVAILABLE -> ""
        }
    }

    /**
     * Enum representing the availability status of biometric authentication.
     */
    enum class BiometricAvailability {
        AVAILABLE,
        NO_HARDWARE,
        HARDWARE_UNAVAILABLE,
        NONE_ENROLLED,
        SECURITY_UPDATE_REQUIRED,
        UNSUPPORTED,
        UNKNOWN
    }
}
