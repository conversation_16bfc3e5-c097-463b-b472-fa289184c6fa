<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/action_home"
        android:icon="@drawable/ic_menu_home"
        android:orderInCategory="70"
        android:title="@string/menu_home"
        app:showAsAction="ifRoom" />

    <!-- Action menu with three-dot icon -->
    <item
        android:id="@+id/action_menu"
        android:icon="@drawable/ic_menu_hamburger"
        android:orderInCategory="80"
        android:title="@string/menu"
        app:showAsAction="ifRoom">
        <menu>
            <item
                android:id="@+id/action_new_transaction"
                android:title="@string/new_transaction" />
            <item
                android:id="@+id/action_new_event"
                android:title="@string/new_event" />
            <item
                android:id="@+id/action_reports"
                android:title="@string/menu_reports" />
            <item
                android:id="@+id/action_search"
                android:title="@string/search" />
            <item
                android:id="@+id/action_profile"
                android:title="@string/menu_profile" />
            <item
                android:id="@+id/action_logout"
                android:title="@string/logout" />
        </menu>
    </item>
</menu>
